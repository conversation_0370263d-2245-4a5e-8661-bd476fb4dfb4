-- Cart V2 Database Optimizations
-- Add composite indexes and stored procedures for improved cart performance

-- =====================================================
-- COMPOSITE INDEXES FOR BETTER QUERY PERFORMANCE
-- =====================================================

-- Index for finding active carts by user
CREATE INDEX IF NOT EXISTS idx_carts_user_status 
ON carts(user_id, status) 
WHERE user_id IS NOT NULL;

-- Index for finding active carts by session
CREATE INDEX IF NOT EXISTS idx_carts_session_status 
ON carts(session_id, status) 
WHERE session_id IS NOT NULL;

-- Index for cart items by cart_id (for cart fetching)
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_id 
ON cart_items(cart_id);

-- Index for cart items by product_id (for checking existing items)
CREATE INDEX IF NOT EXISTS idx_cart_items_product_id 
ON cart_items(product_id);

-- Composite index for cart items by cart and product (for upsert operations)
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_product 
ON cart_items(cart_id, product_id);

-- Index for cart updated_at (for ordering and cleanup)
CREATE INDEX IF NOT EXISTS idx_carts_updated_at 
ON carts(updated_at);

-- =====================================================
-- STORED PROCEDURES FOR CART OPERATIONS
-- =====================================================

-- Function to add item to cart with upsert logic
CREATE OR REPLACE FUNCTION add_to_cart_v2(
    p_product_id UUID,
    p_quantity INTEGER,
    p_user_id UUID DEFAULT NULL,
    p_session_id TEXT DEFAULT NULL
) RETURNS TABLE(
    cart_item_id UUID,
    cart_id UUID,
    product_id UUID,
    quantity INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    product_title TEXT,
    product_price DECIMAL,
    product_discount_price DECIMAL,
    product_images TEXT[],
    product_category TEXT,
    product_coffee_type TEXT,
    product_brand TEXT,
    product_slug TEXT,
    product_is_available BOOLEAN
) LANGUAGE plpgsql AS $$
DECLARE
    v_cart_id UUID;
    v_cart_item_id UUID;
    v_existing_quantity INTEGER := 0;
    v_new_quantity INTEGER;
BEGIN
    -- Validate inputs
    IF p_quantity <= 0 OR p_quantity > 999 THEN
        RAISE EXCEPTION 'Invalid quantity: %', p_quantity;
    END IF;
    
    IF p_user_id IS NULL AND p_session_id IS NULL THEN
        RAISE EXCEPTION 'Either user_id or session_id must be provided';
    END IF;

    -- Check if product exists and is available
    IF NOT EXISTS (
        SELECT 1 FROM products 
        WHERE id = p_product_id AND is_available = true
    ) THEN
        RAISE EXCEPTION 'Product not available: %', p_product_id;
    END IF;

    -- Get or create cart
    IF p_user_id IS NOT NULL THEN
        SELECT id INTO v_cart_id
        FROM carts
        WHERE user_id = p_user_id AND status = 'active'
        ORDER BY updated_at DESC
        LIMIT 1;
    ELSE
        SELECT id INTO v_cart_id
        FROM carts
        WHERE session_id = p_session_id AND status = 'active' AND user_id IS NULL
        ORDER BY updated_at DESC
        LIMIT 1;
    END IF;

    -- Create cart if it doesn't exist
    IF v_cart_id IS NULL THEN
        INSERT INTO carts (user_id, session_id, status, total_amount)
        VALUES (p_user_id, p_session_id, 'active', 0)
        RETURNING id INTO v_cart_id;
    END IF;

    -- Check if item already exists in cart
    SELECT id, quantity INTO v_cart_item_id, v_existing_quantity
    FROM cart_items
    WHERE cart_id = v_cart_id AND product_id = p_product_id;

    -- Calculate new quantity
    v_new_quantity := COALESCE(v_existing_quantity, 0) + p_quantity;
    
    -- Ensure quantity doesn't exceed maximum
    v_new_quantity := LEAST(v_new_quantity, 999);

    -- Insert or update cart item
    IF v_cart_item_id IS NULL THEN
        INSERT INTO cart_items (cart_id, product_id, quantity)
        VALUES (v_cart_id, p_product_id, v_new_quantity)
        RETURNING id INTO v_cart_item_id;
    ELSE
        UPDATE cart_items
        SET quantity = v_new_quantity, updated_at = NOW()
        WHERE id = v_cart_item_id;
    END IF;

    -- Update cart total
    PERFORM update_cart_total_v2(v_cart_id);

    -- Return cart item with product details
    RETURN QUERY
    SELECT 
        ci.id,
        ci.cart_id,
        ci.product_id,
        ci.quantity,
        ci.created_at,
        ci.updated_at,
        p.title,
        p.price,
        p.discount_price,
        p.images,
        p.category,
        p.coffee_type,
        p.brand,
        p.slug,
        p.is_available
    FROM cart_items ci
    JOIN products p ON ci.product_id = p.id
    WHERE ci.id = v_cart_item_id;
END;
$$;

-- Function to update cart total
CREATE OR REPLACE FUNCTION update_cart_total_v2(p_cart_id UUID)
RETURNS VOID LANGUAGE plpgsql AS $$
DECLARE
    v_total DECIMAL(10,2);
BEGIN
    -- Calculate total from cart items
    SELECT COALESCE(SUM(
        COALESCE(p.discount_price, p.price) * ci.quantity
    ), 0) INTO v_total
    FROM cart_items ci
    JOIN products p ON ci.product_id = p.id
    WHERE ci.cart_id = p_cart_id;

    -- Update cart total
    UPDATE carts
    SET total_amount = v_total, updated_at = NOW()
    WHERE id = p_cart_id;
END;
$$;

-- Function to remove item from cart
CREATE OR REPLACE FUNCTION remove_from_cart_v2(p_cart_item_id UUID)
RETURNS VOID LANGUAGE plpgsql AS $$
DECLARE
    v_cart_id UUID;
BEGIN
    -- Get cart_id before deletion
    SELECT cart_id INTO v_cart_id
    FROM cart_items
    WHERE id = p_cart_item_id;

    IF v_cart_id IS NULL THEN
        RAISE EXCEPTION 'Cart item not found: %', p_cart_item_id;
    END IF;

    -- Delete the cart item
    DELETE FROM cart_items WHERE id = p_cart_item_id;

    -- Update cart total
    PERFORM update_cart_total_v2(v_cart_id);
END;
$$;

-- Function to update cart item quantity
CREATE OR REPLACE FUNCTION update_cart_item_quantity_v2(
    p_cart_item_id UUID,
    p_quantity INTEGER
) RETURNS TABLE(
    cart_item_id UUID,
    cart_id UUID,
    product_id UUID,
    quantity INTEGER,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    product_title TEXT,
    product_price DECIMAL,
    product_discount_price DECIMAL,
    product_images TEXT[],
    product_category TEXT,
    product_coffee_type TEXT,
    product_brand TEXT,
    product_slug TEXT,
    product_is_available BOOLEAN
) LANGUAGE plpgsql AS $$
DECLARE
    v_cart_id UUID;
    v_sanitized_quantity INTEGER;
BEGIN
    -- Validate and sanitize quantity
    IF p_quantity <= 0 THEN
        RAISE EXCEPTION 'Quantity must be positive: %', p_quantity;
    END IF;
    
    v_sanitized_quantity := LEAST(p_quantity, 999);

    -- Get cart_id
    SELECT cart_id INTO v_cart_id
    FROM cart_items
    WHERE id = p_cart_item_id;

    IF v_cart_id IS NULL THEN
        RAISE EXCEPTION 'Cart item not found: %', p_cart_item_id;
    END IF;

    -- Update cart item
    UPDATE cart_items
    SET quantity = v_sanitized_quantity, updated_at = NOW()
    WHERE id = p_cart_item_id;

    -- Update cart total
    PERFORM update_cart_total_v2(v_cart_id);

    -- Return updated cart item with product details
    RETURN QUERY
    SELECT 
        ci.id,
        ci.cart_id,
        ci.product_id,
        ci.quantity,
        ci.created_at,
        ci.updated_at,
        p.title,
        p.price,
        p.discount_price,
        p.images,
        p.category,
        p.coffee_type,
        p.brand,
        p.slug,
        p.is_available
    FROM cart_items ci
    JOIN products p ON ci.product_id = p.id
    WHERE ci.id = p_cart_item_id;
END;
$$;

-- Function to clear cart
CREATE OR REPLACE FUNCTION clear_cart_v2(
    p_user_id UUID DEFAULT NULL,
    p_session_id TEXT DEFAULT NULL
) RETURNS VOID LANGUAGE plpgsql AS $$
DECLARE
    v_cart_id UUID;
BEGIN
    -- Find cart
    IF p_user_id IS NOT NULL THEN
        SELECT id INTO v_cart_id
        FROM carts
        WHERE user_id = p_user_id AND status = 'active'
        ORDER BY updated_at DESC
        LIMIT 1;
    ELSE
        SELECT id INTO v_cart_id
        FROM carts
        WHERE session_id = p_session_id AND status = 'active' AND user_id IS NULL
        ORDER BY updated_at DESC
        LIMIT 1;
    END IF;

    IF v_cart_id IS NOT NULL THEN
        -- Delete all cart items
        DELETE FROM cart_items WHERE cart_id = v_cart_id;
        
        -- Update cart total to 0
        UPDATE carts
        SET total_amount = 0, updated_at = NOW()
        WHERE id = v_cart_id;
    END IF;
END;
$$;

-- =====================================================
-- TRIGGERS FOR AUTOMATIC CART TOTAL UPDATES
-- =====================================================

-- Trigger function to update cart total when cart items change
CREATE OR REPLACE FUNCTION trigger_update_cart_total()
RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
    -- Update cart total for the affected cart
    IF TG_OP = 'DELETE' THEN
        PERFORM update_cart_total_v2(OLD.cart_id);
        RETURN OLD;
    ELSE
        PERFORM update_cart_total_v2(NEW.cart_id);
        RETURN NEW;
    END IF;
END;
$$;

-- Create triggers for automatic cart total updates
DROP TRIGGER IF EXISTS trigger_cart_items_update_total ON cart_items;
CREATE TRIGGER trigger_cart_items_update_total
    AFTER INSERT OR UPDATE OR DELETE ON cart_items
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_cart_total();

-- =====================================================
-- CLEANUP FUNCTIONS
-- =====================================================

-- Function to clean up abandoned carts (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_abandoned_carts()
RETURNS INTEGER LANGUAGE plpgsql AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    -- Delete cart items first (due to foreign key constraints)
    DELETE FROM cart_items
    WHERE cart_id IN (
        SELECT id FROM carts
        WHERE status = 'active'
        AND updated_at < NOW() - INTERVAL '30 days'
    );

    -- Delete abandoned carts
    DELETE FROM carts
    WHERE status = 'active'
    AND updated_at < NOW() - INTERVAL '30 days';

    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    RETURN v_deleted_count;
END;
$$;

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- View for cart performance monitoring
CREATE OR REPLACE VIEW cart_performance_stats AS
SELECT
    'total_active_carts' as metric,
    COUNT(*)::TEXT as value
FROM carts
WHERE status = 'active'

UNION ALL

SELECT
    'total_cart_items' as metric,
    COUNT(*)::TEXT as value
FROM cart_items ci
JOIN carts c ON ci.cart_id = c.id
WHERE c.status = 'active'

UNION ALL

SELECT
    'avg_items_per_cart' as metric,
    ROUND(AVG(item_count), 2)::TEXT as value
FROM (
    SELECT COUNT(*) as item_count
    FROM cart_items ci
    JOIN carts c ON ci.cart_id = c.id
    WHERE c.status = 'active'
    GROUP BY c.id
) cart_stats

UNION ALL

SELECT
    'carts_with_user' as metric,
    COUNT(*)::TEXT as value
FROM carts
WHERE status = 'active' AND user_id IS NOT NULL

UNION ALL

SELECT
    'carts_with_session' as metric,
    COUNT(*)::TEXT as value
FROM carts
WHERE status = 'active' AND session_id IS NOT NULL;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION add_to_cart_v2 TO authenticated, anon;
GRANT EXECUTE ON FUNCTION update_cart_total_v2 TO authenticated, anon;
GRANT EXECUTE ON FUNCTION remove_from_cart_v2 TO authenticated, anon;
GRANT EXECUTE ON FUNCTION update_cart_item_quantity_v2 TO authenticated, anon;
GRANT EXECUTE ON FUNCTION clear_cart_v2 TO authenticated, anon;
GRANT EXECUTE ON FUNCTION cleanup_abandoned_carts TO authenticated;
GRANT SELECT ON cart_performance_stats TO authenticated;
