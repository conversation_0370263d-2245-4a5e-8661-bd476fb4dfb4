'use client'

import { useTranslations } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Truck, CreditCard } from 'lucide-react'
import type { UserAddress } from '@/types'

interface ShippingBillingSectionProps {
  shippingAddress: UserAddress | null
  billingAddress: UserAddress | null
  billingSameAsShipping: boolean
  onShippingAddressChange: (address: UserAddress | null) => void
  onBillingAddressChange: (address: UserAddress | null) => void
  onBillingSameAsShippingChange: (same: boolean) => void
}

export function ShippingBillingSection({
  shippingAddress,
  billingAddress,
  billingSameAsShipping,
  onShippingAddressChange,
  onBillingAddressChange,
  onBillingSameAsShippingChange
}: ShippingBillingSectionProps) {
  const t = useTranslations('admin')

  const handleShippingChange = (field: keyof UserAddress, value: string) => {
    const updatedAddress = {
      ...shippingAddress,
      [field]: value
    } as UserAddress

    onShippingAddressChange(updatedAddress)

    // If billing is same as shipping, update billing too
    if (billingSameAsShipping) {
      onBillingAddressChange(updatedAddress)
    }
  }

  const handleBillingChange = (field: keyof UserAddress, value: string) => {
    const updatedAddress = {
      ...billingAddress,
      [field]: value
    } as UserAddress

    onBillingAddressChange(updatedAddress)
  }

  const handleBillingSameAsShippingChange = (checked: boolean) => {
    onBillingSameAsShippingChange(checked)
    
    if (checked && shippingAddress) {
      onBillingAddressChange(shippingAddress)
    } else if (!checked) {
      onBillingAddressChange({
        id: '',
        user_id: '',
        type: 'billing',
        first_name: '',
        last_name: '',
        street_address: '',
        city: '',
        postal_code: '',
        country: 'Switzerland',
        is_default: false,
        created_at: '',
        updated_at: ''
      })
    }
  }

  return (
    <div className="space-y-6">
      {/* Shipping Address */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            {t('manualOrderPage.shippingSection.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <Label htmlFor="shipping-street">{t('manualOrderPage.shippingSection.street')}</Label>
              <Input
                id="shipping-street"
                value={shippingAddress?.street_address || ''}
                onChange={(e) => handleShippingChange('street_address', e.target.value)}
                placeholder="Street address"
                required
              />
            </div>
            <div>
              <Label htmlFor="shipping-city">{t('manualOrderPage.shippingSection.city')}</Label>
              <Input
                id="shipping-city"
                value={shippingAddress?.city || ''}
                onChange={(e) => handleShippingChange('city', e.target.value)}
                placeholder="City"
                required
              />
            </div>
            <div>
              <Label htmlFor="shipping-postal">{t('manualOrderPage.shippingSection.postalCode')}</Label>
              <Input
                id="shipping-postal"
                value={shippingAddress?.postal_code || ''}
                onChange={(e) => handleShippingChange('postal_code', e.target.value)}
                placeholder="Postal code"
                required
              />
            </div>
            <div className="col-span-2">
              <Label htmlFor="shipping-country">{t('manualOrderPage.shippingSection.country')}</Label>
              <Input
                id="shipping-country"
                value={shippingAddress?.country || 'Switzerland'}
                onChange={(e) => handleShippingChange('country', e.target.value)}
                placeholder="Country"
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Billing Address */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            {t('manualOrderPage.billingSection.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Same as Shipping Checkbox */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="billing-same"
              checked={billingSameAsShipping}
              onCheckedChange={handleBillingSameAsShippingChange}
            />
            <Label htmlFor="billing-same">
              {t('manualOrderPage.billingSection.sameAsShipping')}
            </Label>
          </div>

          {/* Billing Address Form */}
          {!billingSameAsShipping && (
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2">
                <Label htmlFor="billing-street">{t('manualOrderPage.shippingSection.street')}</Label>
                <Input
                  id="billing-street"
                  value={billingAddress?.street_address || ''}
                  onChange={(e) => handleBillingChange('street_address', e.target.value)}
                  placeholder="Street address"
                  required
                />
              </div>
              <div>
                <Label htmlFor="billing-city">{t('manualOrderPage.shippingSection.city')}</Label>
                <Input
                  id="billing-city"
                  value={billingAddress?.city || ''}
                  onChange={(e) => handleBillingChange('city', e.target.value)}
                  placeholder="City"
                  required
                />
              </div>
              <div>
                <Label htmlFor="billing-postal">{t('manualOrderPage.shippingSection.postalCode')}</Label>
                <Input
                  id="billing-postal"
                  value={billingAddress?.postal_code || ''}
                  onChange={(e) => handleBillingChange('postal_code', e.target.value)}
                  placeholder="Postal code"
                  required
                />
              </div>
              <div className="col-span-2">
                <Label htmlFor="billing-country">{t('manualOrderPage.shippingSection.country')}</Label>
                <Input
                  id="billing-country"
                  value={billingAddress?.country || 'Switzerland'}
                  onChange={(e) => handleBillingChange('country', e.target.value)}
                  placeholder="Country"
                  required
                />
              </div>
            </div>
          )}

          {/* Display billing address when same as shipping */}
          {billingSameAsShipping && shippingAddress && (
            <div className="p-3 bg-muted rounded-lg text-sm">
              <div className="font-medium mb-1">Billing address (same as shipping):</div>
              <div>{shippingAddress.street_address}</div>
              <div>{shippingAddress.postal_code} {shippingAddress.city}</div>
              <div>{shippingAddress.country}</div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
