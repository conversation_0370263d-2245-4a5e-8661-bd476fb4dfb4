'use client'

import { useState, useCallback } from 'react'
import { useTranslations } from 'next-intl'
import { Minus, Plus, Trash2, Loader2 } from 'lucide-react'
import Image from 'next/image'

import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'

import type { CartItem } from '../types'
import { useUpdateCartItemMutation, useRemoveFromCartMutation } from '../queries'
import { formatCurrency } from '@/lib/utils'

interface CartItemV2Props {
  item: CartItem
  compact?: boolean
}

export function CartItemV2({ item, compact = false }: CartItemV2Props) {
  const t = useTranslations('cart')
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)

  const updateMutation = useUpdateCartItemMutation()
  const removeMutation = useRemoveFromCartMutation()

  const handleQuantityChange = useCallback(async (newQuantity: number) => {
    if (newQuantity < 1 || newQuantity === item.quantity || isUpdating) return

    setIsUpdating(true)
    try {
      await updateMutation.mutateAsync({
        item_id: item.id,
        quantity: newQuantity,
      })
    } catch (error) {
      console.error('Failed to update quantity:', error)
      toast({
        title: t('error'),
        description: t('updateQuantityError'),
        variant: 'destructive',
      })
    } finally {
      setIsUpdating(false)
    }
  }, [item.id, item.quantity, isUpdating, updateMutation, toast, t])

  const handleRemove = useCallback(async () => {
    if (isUpdating) return

    setIsUpdating(true)
    try {
      await removeMutation.mutateAsync({ item_id: item.id })
      toast({
        title: t('itemRemoved'),
        description: t('itemRemovedDescription', { name: item.product?.title || '' }),
      })
    } catch (error) {
      console.error('Failed to remove item:', error)
      toast({
        title: t('error'),
        description: t('removeItemError'),
        variant: 'destructive',
      })
    } finally {
      setIsUpdating(false)
    }
  }, [item.id, isUpdating, removeMutation, toast, t, item.product?.title])

  const product = item.product
  if (!product) return null

  const currentPrice = product.discount_price || product.price
  const originalPrice = product.discount_price ? product.price : null
  const totalPrice = currentPrice * item.quantity

  const hasDiscount = Boolean(product.discount_price)
  const discountPercentage = hasDiscount
    ? Math.round(((product.price - product.discount_price!) / product.price) * 100)
    : 0

  const isLoading = isUpdating || updateMutation.isPending || removeMutation.isPending

  return (
    <Card className={`transition-all duration-200 hover:shadow-md mobile-cart-item ${compact ? 'p-3' : 'p-4 sm:p-4'} ${
      isLoading ? 'opacity-75' : ''
    }`}>
      <div className="flex gap-3 sm:gap-3">
        {/* Product Image - Mobile Optimized */}
        <div className={`relative bg-muted rounded-md overflow-hidden flex-shrink-0 transition-transform duration-200 hover:scale-105 ${
          compact ? 'w-16 h-16' : 'w-24 h-24 sm:w-20 sm:h-20'
        }`}>
          {product.images?.[0] ? (
            <Image
              src={product.images[0]}
              alt={product.title}
              fill
              className="object-cover"
              sizes={compact ? "64px" : "80px"}
              priority={false}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted to-muted/50">
              <div className="w-6 h-6 bg-muted-foreground/20 rounded animate-pulse" />
            </div>
          )}

          {/* Discount Badge */}
          {hasDiscount && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 text-xs px-1 py-0 animate-in zoom-in-95 duration-200"
              aria-label={t('discount', { percentage: discountPercentage })}
            >
              {discountPercentage}%
            </Badge>
          )}

          {/* Loading Overlay */}
          {isLoading && (
            <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
              <Loader2 className="h-4 w-4 animate-spin" />
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start mb-2">
            <div className="min-w-0 flex-1">
              <h4 className={`font-medium truncate ${compact ? 'text-sm' : ''}`}>
                {product.title}
              </h4>
              {product.brand && (
                <p className={`text-muted-foreground ${compact ? 'text-xs' : 'text-sm'}`}>
                  {product.brand}
                </p>
              )}
            </div>
            
            {/* Price */}
            <div className="text-right ml-2">
              <p className={`font-medium ${compact ? 'text-sm' : ''}`}>
                {formatCurrency(totalPrice)}
              </p>
              {originalPrice && (
                <p className={`text-muted-foreground line-through ${compact ? 'text-xs' : 'text-sm'}`}>
                  {formatCurrency(originalPrice * item.quantity)}
                </p>
              )}
            </div>
          </div>

          {/* Quantity Controls - Mobile Optimized */}
          <div className="flex items-center justify-between">
            <div className="flex items-center border rounded-md mobile-quantity-controls">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleQuantityChange(item.quantity - 1)}
                disabled={isLoading || item.quantity <= 1}
                className={`touch-target ${compact ? 'h-8 w-8 p-0' : 'h-10 w-10 p-0 sm:h-8 sm:w-8'}`}
              >
                {isLoading && item.quantity === 1 ? (
                  <Loader2 className="h-4 w-4 animate-spin sm:h-3 sm:w-3" />
                ) : (
                  <Minus className="h-4 w-4 sm:h-3 sm:w-3" />
                )}
              </Button>

              <span className={`px-3 py-1 font-medium min-w-[2.5rem] text-center ${
                compact ? 'text-sm px-2' : 'text-base sm:text-sm'
              }`}>
                {item.quantity}
              </span>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleQuantityChange(item.quantity + 1)}
                disabled={isLoading}
                className={`touch-target ${compact ? 'h-8 w-8 p-0' : 'h-10 w-10 p-0 sm:h-8 sm:w-8'}`}
              >
                {isLoading && item.quantity > 1 ? (
                  <Loader2 className="h-4 w-4 animate-spin sm:h-3 sm:w-3" />
                ) : (
                  <Plus className="h-4 w-4 sm:h-3 sm:w-3" />
                )}
              </Button>
            </div>

            {/* Remove Button - Mobile Optimized */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRemove}
              disabled={isLoading}
              className={`text-destructive hover:text-destructive touch-target ${
                compact ? 'h-8 w-8 p-0' : 'h-10 w-10 p-0 sm:h-8 sm:w-8'
              }`}
            >
              {removeMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin sm:h-3 sm:w-3" />
              ) : (
                <Trash2 className="h-4 w-4 sm:h-3 sm:w-3" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </Card>
  )
}
