export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          first_name: string
          last_name: string
          phone: string | null
          date_of_birth: string | null
          created_at: string
          updated_at: string
          lifetime_spend: number
          current_level: number
          total_points: number
          is_admin: boolean
        }
        Insert: {
          id?: string
          email: string
          first_name: string
          last_name: string
          phone?: string | null
          date_of_birth?: string | null
          created_at?: string
          updated_at?: string
          lifetime_spend?: number
          current_level?: number
          total_points?: number
          is_admin?: boolean
        }
        Update: {
          id?: string
          email?: string
          first_name?: string
          last_name?: string
          phone?: string | null
          date_of_birth?: string | null
          created_at?: string
          updated_at?: string
          lifetime_spend?: number
          current_level?: number
          total_points?: number
          is_admin?: boolean
        }
      }
      user_addresses: {
        Row: {
          id: string
          user_id: string
          type: 'billing' | 'shipping'
          first_name: string
          last_name: string
          company: string | null
          street_address: string
          city: string
          postal_code: string
          country: string
          is_default: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          type: 'billing' | 'shipping'
          first_name: string
          last_name: string
          company?: string | null
          street_address: string
          city: string
          postal_code: string
          country: string
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          type?: 'billing' | 'shipping'
          first_name?: string
          last_name?: string
          company?: string | null
          street_address?: string
          city?: string
          postal_code?: string
          country?: string
          is_default?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          title: string
          description: string
          slug: string
          category: 'coffee' | 'accessories'
          coffee_type: 'capsules' | 'pods' | 'beans' | 'ground' | null
          brand: string | null
          blend: string | null
          machine_compatibility: string[] | null
          pack_quantity: number | null
          pack_weight_grams: number | null
          price: number
          discount_price: number | null
          cost_per_espresso: number | null
          images: string[]
          inventory_count: number
          purchase_cost: number | null
          is_available: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          slug: string
          category: 'coffee' | 'accessories'
          coffee_type?: 'capsules' | 'pods' | 'beans' | 'ground' | null
          brand?: string | null
          blend?: string | null
          machine_compatibility?: string[] | null
          pack_quantity?: number | null
          pack_weight_grams?: number | null
          price: number
          discount_price?: number | null
          cost_per_espresso?: number | null
          images?: string[]
          inventory_count?: number
          purchase_cost?: number | null
          is_available?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          slug?: string
          category?: 'coffee' | 'accessories'
          coffee_type?: 'capsules' | 'pods' | 'beans' | 'ground' | null
          brand?: string | null
          blend?: string | null
          machine_compatibility?: string[] | null
          pack_quantity?: number | null
          pack_weight_grams?: number | null
          price?: number
          discount_price?: number | null
          cost_per_espresso?: number | null
          images?: string[]
          inventory_count?: number
          purchase_cost?: number | null
          is_available?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      bundles: {
        Row: {
          id: string
          title: string
          description: string
          slug: string
          image: string
          total_price: number
          discount_price: number | null
          is_available: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          slug: string
          image: string
          total_price: number
          discount_price?: number | null
          is_available?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          slug?: string
          image?: string
          total_price?: number
          discount_price?: number | null
          is_available?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      category_vat_rates: {
        Row: {
          id: string
          category: 'coffee' | 'accessories'
          vat_rate: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          category: 'coffee' | 'accessories'
          vat_rate?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          category?: 'coffee' | 'accessories'
          vat_rate?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      bundle_items: {
        Row: {
          id: string
          bundle_id: string
          product_id: string
          quantity: number
        }
        Insert: {
          id?: string
          bundle_id: string
          product_id: string
          quantity: number
        }
        Update: {
          id?: string
          bundle_id?: string
          product_id?: string
          quantity?: number
        }
      }
      cart_items: {
        Row: {
          id: string
          user_id: string | null
          session_id: string | null
          product_id: string
          quantity: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          session_id?: string | null
          product_id: string
          quantity: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          session_id?: string | null
          product_id?: string
          quantity?: number
          created_at?: string
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          order_number: string | null
          user_id: string | null
          email: string
          status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          subtotal: number
          shipping_cost: number
          tax_amount: number
          discount_amount: number
          total_amount: number
          currency: string
          payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
          payment_intent_id: string | null
          shipping_address: Json
          billing_address: Json
          tracking_number: string | null
          shipping_carrier: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          order_number?: string | null
          user_id?: string | null
          email: string
          status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          subtotal: number
          shipping_cost: number
          tax_amount: number
          discount_amount: number
          total_amount: number
          currency?: string
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
          payment_intent_id?: string | null
          shipping_address: Json
          billing_address: Json
          tracking_number?: string | null
          shipping_carrier?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          order_number?: string | null
          user_id?: string | null
          email?: string
          status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          subtotal?: number
          shipping_cost?: number
          tax_amount?: number
          discount_amount?: number
          total_amount?: number
          currency?: string
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
          payment_intent_id?: string | null
          shipping_address?: Json
          billing_address?: Json
          tracking_number?: string | null
          shipping_carrier?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
