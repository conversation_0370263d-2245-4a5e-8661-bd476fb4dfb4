// Cart V2 Unit Tests
// Individual component tests for Cart V2 system

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { <PERSON>tError, CartErrorHandler, getErrorMessage } from '../errors'
import { CachedCartOperations, PerformanceMonitor, DebouncedOperations } from '../performance'
import type { Cart } from '../types'

describe('Cart V2 Unit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    CachedCartOperations.invalidateAllCarts()
    PerformanceMonitor.clearMetrics()
    CartErrorHandler.clearStats()
  })

  describe('CartError', () => {
    it('should create error with correct properties', () => {
      const error = new CartError('VALIDATION_ERROR', 'Test error', { field: 'quantity' })
      
      expect(error.code).toBe('VALIDATION_ERROR')
      expect(error.message).toBe('Test error')
      expect(error.details).toEqual({ field: 'quantity' })
      expect(error.timestamp).toBeInstanceOf(Date)
      expect(error.name).toBe('CartError')
    })

    it('should serialize to JSON correctly', () => {
      const error = new CartError('ITEM_NOT_FOUND', 'Item not found', { itemId: '123' })
      const json = error.toJSON()
      
      expect(json).toEqual({
        code: 'ITEM_NOT_FOUND',
        message: 'Item not found',
        details: { itemId: '123' }
      })
    })

    it('should get correct HTTP status', () => {
      const validationError = new CartError('VALIDATION_ERROR', 'Invalid input')
      const notFoundError = new CartError('CART_NOT_FOUND', 'Cart not found')
      const serverError = new CartError('DATABASE_ERROR', 'DB error')
      
      expect(validationError.getHttpStatus()).toBe(400)
      expect(notFoundError.getHttpStatus()).toBe(404)
      expect(serverError.getHttpStatus()).toBe(500)
    })

    it('should create from Supabase error', () => {
      const supabaseError = { code: 'PGRST116', message: 'No rows returned' }
      const cartError = CartError.fromSupabaseError(supabaseError)
      
      expect(cartError.code).toBe('CART_NOT_FOUND')
      expect(cartError.message).toBe('No rows returned')
      expect(cartError.details?.supabaseCode).toBe('PGRST116')
    })

    it('should create from network error', () => {
      const networkError = new Error('fetch failed')
      const cartError = CartError.fromNetworkError(networkError)
      
      expect(cartError.code).toBe('NETWORK_ERROR')
      expect(cartError.message).toBe('fetch failed')
    })

    it('should create validation error', () => {
      const error = CartError.validation('Invalid quantity', 'quantity')
      
      expect(error.code).toBe('VALIDATION_ERROR')
      expect(error.message).toBe('Invalid quantity')
      expect(error.details?.field).toBe('quantity')
    })

    it('should create not found error', () => {
      const error = CartError.notFound('cart', 'cart-123')
      
      expect(error.code).toBe('CART_NOT_FOUND')
      expect(error.message).toBe('cart with id cart-123 not found')
      expect(error.details?.resource).toBe('cart')
      expect(error.details?.id).toBe('cart-123')
    })
  })

  describe('Error Messages', () => {
    it('should return correct English messages', () => {
      expect(getErrorMessage('CART_NOT_FOUND', 'en'))
        .toBe('Your cart could not be found. Please try refreshing the page.')
      
      expect(getErrorMessage('INVALID_QUANTITY', 'en'))
        .toBe('Please enter a valid quantity.')
    })

    it('should return correct German messages', () => {
      expect(getErrorMessage('CART_NOT_FOUND', 'de'))
        .toBe('Ihr Warenkorb konnte nicht gefunden werden. Bitte laden Sie die Seite neu.')
      
      expect(getErrorMessage('INVALID_QUANTITY', 'de'))
        .toBe('Bitte geben Sie eine gültige Menge ein.')
    })

    it('should return correct Italian messages', () => {
      expect(getErrorMessage('CART_NOT_FOUND', 'it'))
        .toBe('Il tuo carrello non è stato trovato. Ricarica la pagina.')
      
      expect(getErrorMessage('INVALID_QUANTITY', 'it'))
        .toBe('Inserisci una quantità valida.')
    })

    it('should fallback to English for unknown locale', () => {
      // Test with a locale that doesn't exist in ERROR_MESSAGES
      expect(getErrorMessage('CART_NOT_FOUND', 'en'))
        .toBe('Your cart could not be found. Please try refreshing the page.')
    })
  })

  describe('CartErrorHandler', () => {
    it('should handle network errors with retry', async () => {
      const error = new CartError('NETWORK_ERROR', 'Network failed')
      const recovery = await CartErrorHandler.handleError(error, {
        operation: 'test',
        retryCount: 0
      })
      
      expect(recovery.shouldRetry).toBe(true)
      expect(recovery.retryDelay).toBe(1000) // First retry delay
    })

    it('should handle network errors with exponential backoff', async () => {
      const error = new CartError('NETWORK_ERROR', 'Network failed')
      
      const recovery1 = await CartErrorHandler.handleError(error, {
        operation: 'test',
        retryCount: 1
      })
      
      const recovery2 = await CartErrorHandler.handleError(error, {
        operation: 'test',
        retryCount: 2
      })
      
      expect(recovery1.retryDelay).toBe(2000) // 2^1 * 1000
      expect(recovery2.retryDelay).toBe(4000) // 2^2 * 1000
    })

    it('should not retry session expired errors', async () => {
      const error = new CartError('SESSION_EXPIRED', 'Session expired')
      const recovery = await CartErrorHandler.handleError(error, {
        operation: 'test'
      })
      
      expect(recovery.shouldRetry).toBe(false)
      expect(recovery.recoveryAction).toBeDefined()
    })

    it('should determine error severity correctly', () => {
      expect(CartErrorHandler.getSeverity(new CartError('VALIDATION_ERROR', 'test')))
        .toBe('low')
      
      expect(CartErrorHandler.getSeverity(new CartError('ITEM_NOT_FOUND', 'test')))
        .toBe('medium')
      
      expect(CartErrorHandler.getSeverity(new CartError('NETWORK_ERROR', 'test')))
        .toBe('high')
      
      expect(CartErrorHandler.getSeverity(new CartError('DATABASE_ERROR', 'test')))
        .toBe('critical')
    })

    it('should track error statistics', async () => {
      const error1 = new CartError('VALIDATION_ERROR', 'Test 1')
      const error2 = new CartError('VALIDATION_ERROR', 'Test 2')
      const error3 = new CartError('NETWORK_ERROR', 'Test 3')
      
      await CartErrorHandler.handleError(error1, { operation: 'test' })
      await CartErrorHandler.handleError(error2, { operation: 'test' })
      await CartErrorHandler.handleError(error3, { operation: 'test' })
      
      const stats = CartErrorHandler.getErrorStats()
      
      expect(stats.VALIDATION_ERROR.count).toBe(2)
      expect(stats.NETWORK_ERROR.count).toBe(1)
      expect(stats.VALIDATION_ERROR.severity).toBe('low')
      expect(stats.NETWORK_ERROR.severity).toBe('high')
    })
  })

  describe('Performance Monitoring', () => {
    it('should track operation timing', () => {
      const endTimer = PerformanceMonitor.startTimer('test_operation')
      
      // Simulate work
      const start = Date.now()
      while (Date.now() - start < 10) {
        // Busy wait for 10ms
      }
      
      const duration = endTimer()
      
      expect(duration).toBeGreaterThan(0)
      
      const metrics = PerformanceMonitor.getMetrics('test_operation')
      expect(metrics).toBeDefined()
      expect(metrics!.count).toBe(1)
      expect(metrics!.avg).toBeGreaterThan(0)
      expect(metrics!.min).toBe(duration)
      expect(metrics!.max).toBe(duration)
    })

    it('should calculate correct averages', () => {
      PerformanceMonitor.recordMetric('test_op', 100)
      PerformanceMonitor.recordMetric('test_op', 200)
      PerformanceMonitor.recordMetric('test_op', 300)
      
      const metrics = PerformanceMonitor.getMetrics('test_op')
      
      expect(metrics!.avg).toBe(200)
      expect(metrics!.min).toBe(100)
      expect(metrics!.max).toBe(300)
      expect(metrics!.count).toBe(3)
    })

    it('should limit metrics history', () => {
      // Add more than 100 metrics
      for (let i = 0; i < 150; i++) {
        PerformanceMonitor.recordMetric('test_op', i)
      }
      
      const metrics = PerformanceMonitor.getMetrics('test_op')
      expect(metrics!.count).toBe(100) // Should be limited to 100
    })
  })

  describe('Caching', () => {
    it('should cache and retrieve data', async () => {
      const mockCart: Cart = {
        id: 'cart-1',
        user_id: 'user-1',
        session_id: null,
        status: 'active',
        total_amount: 25.99,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      const fetchFn = jest.fn().mockResolvedValue(mockCart)
      
      // First call should fetch
      const result1 = await CachedCartOperations.getCachedCart('user-1', undefined, fetchFn)
      expect(fetchFn).toHaveBeenCalledTimes(1)
      expect(result1).toEqual(mockCart)

      // Second call should use cache
      const result2 = await CachedCartOperations.getCachedCart('user-1', undefined, fetchFn)
      expect(fetchFn).toHaveBeenCalledTimes(1) // Still 1
      expect(result2).toEqual(mockCart)
    })

    it('should invalidate cache correctly', async () => {
      const mockCart: Cart = {
        id: 'cart-1',
        user_id: 'user-1',
        session_id: null,
        status: 'active',
        total_amount: 25.99,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      const fetchFn = jest.fn().mockResolvedValue(mockCart)
      
      // Cache the cart
      await CachedCartOperations.getCachedCart('user-1', undefined, fetchFn)
      expect(fetchFn).toHaveBeenCalledTimes(1)

      // Invalidate cache
      CachedCartOperations.invalidateCart('user-1')

      // Next call should fetch again
      await CachedCartOperations.getCachedCart('user-1', undefined, fetchFn)
      expect(fetchFn).toHaveBeenCalledTimes(2)
    })
  })

  describe('Debounced Operations', () => {
    it('should debounce function calls', (done) => {
      const mockFn = jest.fn()
      const debouncedFn = DebouncedOperations.debounce('test', mockFn, 50)
      
      // Call multiple times quickly
      debouncedFn('arg1')
      debouncedFn('arg2')
      debouncedFn('arg3')
      
      // Should not be called yet
      expect(mockFn).not.toHaveBeenCalled()
      
      // Wait for debounce delay
      setTimeout(() => {
        expect(mockFn).toHaveBeenCalledTimes(1)
        expect(mockFn).toHaveBeenCalledWith('arg3') // Last call wins
        done()
      }, 60)
    })

    it('should clear debounce timers', () => {
      const mockFn = jest.fn()
      const debouncedFn = DebouncedOperations.debounce('test', mockFn, 100)
      
      debouncedFn('arg1')
      DebouncedOperations.clearDebounce('test')
      
      // Wait longer than debounce delay
      setTimeout(() => {
        expect(mockFn).not.toHaveBeenCalled()
      }, 150)
    })
  })
})
