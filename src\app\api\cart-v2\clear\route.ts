// Cart V2 Clear API Route
// Clear entire cart for authenticated user or session

import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getServerCartSessionId } from '@/lib/cart-session'
import { CartServiceV2 } from '@/lib/cart-v2/server-service'
import { CartError } from '@/lib/cart-v2/errors'

/**
 * POST /api/cart-v2/clear
 * Clear entire cart
 */
export async function POST() {
  try {
    const supabase = await createClient()
    const cartService = new CartServiceV2(supabase)
    
    // Get user if authenticated
    const { data: { user } } = await supabase.auth.getUser()
    const userId = user?.id
    
    // Get session ID for guest users
    const sessionId = userId ? null : await getServerCartSessionId()
    
    console.log('🛒 Cart V2 API: POST clear cart', { 
      userId: userId || 'guest', 
      sessionId 
    })
    
    await cartService.clearCart(userId || undefined, sessionId || undefined)
    
    return NextResponse.json({ success: true })
    
  } catch (error) {
    console.error('🛒 Cart V2 API: Clear error:', error)
    
    if (error instanceof CartError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.getHttpStatus() }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
