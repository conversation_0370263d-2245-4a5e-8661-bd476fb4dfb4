'use client'

import { useState, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Receipt, Tag, X } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'

interface AppliedCoupon {
  id: string
  code: string
  type: 'percentage' | 'fixed_amount'
  value: number
  discountAmount: number
}

interface OrderSummarySectionProps {
  subtotal: number
  shippingCost: number
  taxAmount: number
  discountAmount: number
  total: number
  appliedCoupon: AppliedCoupon | null
  onCouponChange: (coupon: AppliedCoupon | null) => void
}

export function OrderSummarySection({
  subtotal,
  shippingCost,
  taxAmount,
  discountAmount,
  total,
  appliedCoupon,
  onCouponChange
}: OrderSummarySectionProps) {
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])
  const { toast } = useToast()

  const [couponCode, setCouponCode] = useState('')
  const [applyingCoupon, setApplyingCoupon] = useState(false)

  const applyCoupon = async () => {
    if (!couponCode.trim()) return

    setApplyingCoupon(true)
    try {
      const { data: coupon, error } = await supabase
        .from('coupons')
        .select('*')
        .eq('code', couponCode.trim().toUpperCase())
        .eq('is_active', true)
        .single()

      if (error || !coupon) {
        toast({
          title: 'Invalid Coupon',
          description: 'The coupon code is invalid or has expired.',
          variant: 'destructive'
        })
        return
      }

      // Check if coupon is still valid
      const now = new Date()
      const validFrom = coupon.valid_from ? new Date(coupon.valid_from) : null
      const validUntil = coupon.valid_until ? new Date(coupon.valid_until) : null

      if (validFrom && now < validFrom) {
        toast({
          title: 'Coupon Not Yet Valid',
          description: 'This coupon is not yet valid.',
          variant: 'destructive'
        })
        return
      }

      if (validUntil && now > validUntil) {
        toast({
          title: 'Coupon Expired',
          description: 'This coupon has expired.',
          variant: 'destructive'
        })
        return
      }

      // Check minimum order amount
      if (coupon.minimum_order_amount && subtotal < coupon.minimum_order_amount) {
        toast({
          title: 'Minimum Order Not Met',
          description: `This coupon requires a minimum order of ${formatCurrency(coupon.minimum_order_amount)}.`,
          variant: 'destructive'
        })
        return
      }

      // Check usage limits
      if (coupon.usage_limit && coupon.usage_count >= coupon.usage_limit) {
        toast({
          title: 'Coupon Usage Limit Reached',
          description: 'This coupon has reached its usage limit.',
          variant: 'destructive'
        })
        return
      }

      // Calculate discount amount
      let discountAmount = 0
      if (coupon.type === 'percentage') {
        discountAmount = (subtotal * coupon.value) / 100
        if (coupon.max_discount_amount) {
          discountAmount = Math.min(discountAmount, coupon.max_discount_amount)
        }
      } else if (coupon.type === 'fixed_amount') {
        discountAmount = Math.min(coupon.value, subtotal)
      }

      const appliedCouponData: AppliedCoupon = {
        id: coupon.id,
        code: coupon.code,
        type: coupon.type,
        value: coupon.value,
        discountAmount
      }

      onCouponChange(appliedCouponData)
      setCouponCode('')
      
      toast({
        title: 'Coupon Applied',
        description: `Discount of ${formatCurrency(discountAmount)} applied.`,
      })
    } catch (error) {
      console.error('Error applying coupon:', error)
      toast({
        title: 'Error',
        description: 'Failed to apply coupon. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setApplyingCoupon(false)
    }
  }

  const removeCoupon = () => {
    onCouponChange(null)
    toast({
      title: 'Coupon Removed',
      description: 'The coupon has been removed from your order.',
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Receipt className="h-5 w-5" />
          {t('manualOrderPage.orderSummary.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Coupon Section */}
        <div className="space-y-3">
          {!appliedCoupon ? (
            <div className="flex gap-2">
              <Input
                placeholder={t('manualOrderPage.orderSummary.couponCode')}
                value={couponCode}
                onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                onKeyDown={(e) => e.key === 'Enter' && applyCoupon()}
              />
              <Button
                onClick={applyCoupon}
                disabled={!couponCode.trim() || applyingCoupon}
                size="sm"
              >
                <Tag className="h-4 w-4 mr-1" />
                {applyingCoupon ? 'Applying...' : t('manualOrderPage.orderSummary.applyCoupon')}
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-green-600" />
                <div>
                  <div className="font-medium text-green-800">{appliedCoupon.code}</div>
                  <div className="text-sm text-green-600">
                    {appliedCoupon.type === 'percentage' 
                      ? `${appliedCoupon.value}% off`
                      : `${formatCurrency(appliedCoupon.value)} off`
                    }
                  </div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={removeCoupon}
                className="text-green-600 hover:text-green-800"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        <Separator />

        {/* Order Totals */}
        <div className="space-y-3">
          <div className="flex justify-between">
            <span>{t('manualOrderPage.orderSummary.subtotal')}</span>
            <span>{formatCurrency(subtotal)}</span>
          </div>

          <div className="flex justify-between">
            <span className="flex items-center gap-2">
              {t('manualOrderPage.orderSummary.shipping')}
              {shippingCost === 0 && (
                <Badge variant="secondary" className="text-xs">
                  Free shipping over CHF 90
                </Badge>
              )}
            </span>
            <span>{formatCurrency(shippingCost)}</span>
          </div>

          {taxAmount > 0 && (
            <div className="flex justify-between">
              <span>{t('manualOrderPage.orderSummary.tax')}</span>
              <span>{formatCurrency(taxAmount)}</span>
            </div>
          )}

          {discountAmount > 0 && (
            <div className="flex justify-between text-green-600">
              <span>{t('manualOrderPage.orderSummary.discount')}</span>
              <span>-{formatCurrency(discountAmount)}</span>
            </div>
          )}

          <Separator />

          <div className="flex justify-between text-lg font-semibold">
            <span>{t('manualOrderPage.orderSummary.total')}</span>
            <span>{formatCurrency(total)}</span>
          </div>
        </div>

        {/* Order Notes */}
        <div className="text-sm text-muted-foreground space-y-1">
          <div>• Payment will be marked as completed</div>
          <div>• Customer will receive order confirmation email</div>
          <div>• Admin will receive order notification email</div>
        </div>
      </CardContent>
    </Card>
  )
}
