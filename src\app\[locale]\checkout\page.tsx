'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { StripeProvider } from '@/components/checkout/stripe-provider'
import { StripePaymentForm } from '@/components/checkout/stripe-payment-form'

import { ArrowLeft, Loader2 } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { useCartQuery, useCartSummary, useClearCartMutation } from '@/lib/cart-v2/queries'
import { useToast } from '@/hooks/use-toast'
import Link from 'next/link'

interface CheckoutFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  shippingAddress: {
    street: string
    city: string
    postalCode: string
    country: string
  }
  billingAddress: {
    street: string
    city: string
    postalCode: string
    country: string
  }
  billingSameAsShipping: boolean
  createAccount: boolean
  marketingConsent: boolean
}

export default function CheckoutPage() {
  console.log('🛒 CheckoutPage: Component rendering')
  const router = useRouter()
  const { data: cart, isLoading: cartLoading } = useCartQuery()
  const { data: summary } = useCartSummary()
  const clearCartMutation = useClearCartMutation()
  const { toast } = useToast()
  const t = useTranslations('checkout')
  const locale = useLocale()
  const [loading, setLoading] = useState(false)
  const [skipCartRedirect, setSkipCartRedirect] = useState(false)
  const [paymentIntent, setPaymentIntent] = useState<{
    id: string
    client_secret: string
    status: string
  } | null>(null)
  const [showPaymentForm, setShowPaymentForm] = useState(false)

  // Coupon states
  const [couponCode, setCouponCode] = useState('')
  const [appliedCoupon, setAppliedCoupon] = useState<{
    id: string
    code: string
    type: string
    value: number
    discountAmount: number
  } | null>(null)
  const [couponLoading, setCouponLoading] = useState(false)

  console.log('🛒 CheckoutPage: State', {
    cartLoading,
    cartItemsCount: cart?.items?.length || 0,
    cartId: cart?.id,
    locale,
    appliedCoupon
  })
  const [formData, setFormData] = useState<CheckoutFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    shippingAddress: {
      street: '',
      city: '',
      postalCode: '',
      country: 'CH'
    },
    billingAddress: {
      street: '',
      city: '',
      postalCode: '',
      country: 'CH'
    },
    billingSameAsShipping: true,
    createAccount: false,
    marketingConsent: false
  })

  // Use Cart V2 summary calculations
  const subtotal = summary?.subtotal || 0
  const shippingCost = summary?.shipping_cost || 0
  const discountAmount = appliedCoupon?.discountAmount || 0
  const total = subtotal + shippingCost - discountAmount // Total is VAT-inclusive

  // Calculate VAT breakdown for display (VAT is included in prices)
  const taxRate = 0.077 // 7.7% Swiss VAT
  const taxAmount = total - (total / (1 + taxRate)) // VAT amount from inclusive price

  useEffect(() => {
    console.log('🛒 CheckoutPage: useEffect triggered', {
      cartLoading,
      cartItemsLength: cart?.items?.length || 0,
      skipCartRedirect
    })

    if (!skipCartRedirect && !cartLoading && (!cart || !cart.items || cart.items.length === 0)) {
      console.log('🛒 CheckoutPage: Redirecting to cart - no items')
      router.push(`/${locale}/cart`)
    }
  }, [cart, cartLoading, router, locale, skipCartRedirect])

  const handleInputChange = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof CheckoutFormData] as Record<string, unknown>),
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const handleCheckboxChange = (field: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked
    }))

    // Copy shipping to billing if checkbox is checked
    if (field === 'billingSameAsShipping' && checked) {
      setFormData(prev => ({
        ...prev,
        billingAddress: { ...prev.shippingAddress }
      }))
    }
  }

  const validateForm = () => {
    const required = [
      'firstName', 'lastName', 'email', 'phone',
      'shippingAddress.street', 'shippingAddress.city', 'shippingAddress.postalCode'
    ]

    for (const field of required) {
      if (field.includes('.')) {
        const [parent, child] = field.split('.')
        const parentValue = formData[parent as keyof CheckoutFormData] as Record<string, string>
        if (!parentValue[child]) {
          return false
        }
      } else {
        if (!formData[field as keyof CheckoutFormData]) {
          return false
        }
      }
    }

    if (!formData.billingSameAsShipping) {
      const billingRequired = [
        'billingAddress.street', 'billingAddress.city', 'billingAddress.postalCode'
      ]
      for (const field of billingRequired) {
        const [parent, child] = field.split('.')
        const parentValue = formData[parent as keyof CheckoutFormData] as Record<string, string>
        if (!parentValue[child]) {
          return false
        }
      }
    }

    return true
  }

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast({
        title: t('error'),
        description: t('enterCouponCode'),
        variant: 'destructive'
      })
      return
    }

    setCouponLoading(true)

    try {
      const response = await fetch('/api/checkout/validate-coupon', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: couponCode.trim(),
          orderAmount: subtotal + shippingCost
        }),
      })

      const result = await response.json()

      if (result.success) {
        setAppliedCoupon(result.coupon)
        toast({
          title: t('couponApplied'),
          description: t('couponSaved', { amount: formatCurrency(result.discountAmount) }),
          variant: 'default'
        })
      } else {
        toast({
          title: t('error'),
          description: result.error || t('invalidCoupon'),
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error applying coupon:', error)
      toast({
        title: t('error'),
        description: t('invalidCoupon'),
        variant: 'destructive'
      })
    } finally {
      setCouponLoading(false)
    }
  }

  const handleRemoveCoupon = () => {
    setAppliedCoupon(null)
    setCouponCode('')
    toast({
      title: t('couponRemoved'),
      description: t('couponRemovedDesc'),
      variant: 'default'
    })
  }

  const handleCreateOrder = async () => {
    if (!validateForm()) {
      toast({
        title: t('error'),
        description: t('fillRequiredFields'),
        variant: 'destructive'
      })
      return
    }

    setLoading(true)

    try {
      const orderData = {
        cartId: cart?.id,
        customerInfo: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        shippingAddress: formData.shippingAddress,
        billingAddress: formData.billingSameAsShipping
          ? formData.shippingAddress
          : formData.billingAddress,
        total: total,
        coupon: appliedCoupon ? {
          id: appliedCoupon.id,
          code: appliedCoupon.code,
          discountAmount: appliedCoupon.discountAmount
        } : null
      }

      const response = await fetch('/api/checkout/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      })

      const result = await response.json()

      if (result.success && result.paymentIntent) {
        setPaymentIntent(result.paymentIntent)
        setShowPaymentForm(true)
      } else {
        toast({
          title: t('error'),
          description: result.error || t('orderCreationFailed'),
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Order creation error:', error)
      toast({
        title: t('error'),
        description: t('unexpectedError'),
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePaymentSuccess = (paymentIntentId: string) => {
    setSkipCartRedirect(true)
    clearCartMutation.mutate()
    router.push(`/${locale}/checkout/success?payment_intent=${paymentIntentId}`)
  }

  const handlePaymentError = (error: string) => {
    toast({
      title: t('paymentFailed'),
      description: error,
      variant: 'destructive'
    })
  }

  if (cartLoading) {
    console.log('🛒 CheckoutPage: Showing loading state')
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Caricamento checkout...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!cart || cart.items.length === 0) {
    console.log('🛒 CheckoutPage: Showing empty cart state')
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <p className="mb-4">Il carrello è vuoto</p>
              <Button onClick={() => router.push(`/${locale}/shop`)}>
                Continua lo shopping
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Button variant="ghost" asChild className="mb-4">
          <Link href={`/${locale}/cart`} prefetch={false}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('backToCart')}
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">{t('title')}</h1>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Checkout Form */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('contactInfo')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">{t('firstName')} {t('required')}</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">{t('lastName')} {t('required')}</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="email">{t('email')} {t('required')}</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone">{t('phone')} {t('required')}</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  required
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('shippingAddress')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="shippingStreet">{t('street')} {t('required')}</Label>
                <Input
                  id="shippingStreet"
                  value={formData.shippingAddress.street}
                  onChange={(e) => handleInputChange('shippingAddress.street', e.target.value)}
                  required
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="shippingPostalCode">{t('postalCode')} {t('required')}</Label>
                  <Input
                    id="shippingPostalCode"
                    value={formData.shippingAddress.postalCode}
                    onChange={(e) => handleInputChange('shippingAddress.postalCode', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="shippingCity">{t('city')} {t('required')}</Label>
                  <Input
                    id="shippingCity"
                    value={formData.shippingAddress.city}
                    onChange={(e) => handleInputChange('shippingAddress.city', e.target.value)}
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="billingSameAsShipping"
              checked={formData.billingSameAsShipping}
              onCheckedChange={(checked) => handleCheckboxChange('billingSameAsShipping', checked as boolean)}
            />
            <Label htmlFor="billingSameAsShipping">
              {t('billingSameAsShipping')}
            </Label>
          </div>
        </div>

        {/* Order Summary */}
        <div>
          <Card className="sticky top-4">
            <CardHeader>
              <CardTitle>{t('orderSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {cart.items.map((item) => (
                  <div key={item.id} className="flex justify-between items-center">
                    <div className="flex-1">
                      <p className="font-medium">{item.product?.title}</p>
                      <p className="text-sm text-gray-600">{t('quantity')}: {item.quantity}</p>
                    </div>
                    <p className="font-medium">
                      {formatCurrency((item.product?.discount_price || item.product?.price || 0) * item.quantity)}
                    </p>
                  </div>
                ))}
              </div>



              <div className="border-t pt-4 space-y-2">
                <div className="flex justify-between">
                  <span>{t('subtotal')}:</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('shipping')}:</span>
                  <span>{shippingCost === 0 ? t('free') : formatCurrency(shippingCost)}</span>
                </div>
                {appliedCoupon && (
                  <div className="flex justify-between text-green-600">
                    <span>{t('discount')} ({appliedCoupon.code}):</span>
                    <span>-{formatCurrency(appliedCoupon.discountAmount)}</span>
                  </div>
                )}
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>{t('total')}:</span>
                  <span>{formatCurrency(total)}</span>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>{t('vatIncluded', { rate: (taxRate * 100).toFixed(1) })}:</span>
                  <span>{formatCurrency(taxAmount)}</span>
                </div>
              </div>

              {/* Coupon Section */}
              <div className="border-t pt-4">
                <div className="space-y-3">
                  <Label htmlFor="couponCode">{t('couponCode')}</Label>
                  {!appliedCoupon ? (
                    <div className="flex gap-2">
                      <Input
                        id="couponCode"
                        placeholder={t('enterCouponCode')}
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                        disabled={couponLoading}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleApplyCoupon}
                        disabled={couponLoading || !couponCode.trim()}
                      >
                        {couponLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          t('applyCoupon')
                        )}
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="flex items-center gap-2">
                        <span className="text-green-700 font-medium">
                          {appliedCoupon.code}
                        </span>
                        <span className="text-green-600 text-sm">
                          -{formatCurrency(appliedCoupon.discountAmount)}
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleRemoveCoupon}
                        className="text-green-700 hover:text-green-800"
                      >
                        {t('removeCoupon')}
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {!showPaymentForm ? (
                <Button
                  onClick={handleCreateOrder}
                  className="w-full"
                  size="lg"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('processing')}
                    </>
                  ) : (
                    t('continueToPayment')
                  )}
                </Button>
              ) : null}
            </CardContent>
          </Card>

          {/* Payment Form */}
          {showPaymentForm && paymentIntent && (
            <div className="mt-6">
              <StripeProvider clientSecret={paymentIntent.client_secret}>
                <StripePaymentForm
                  clientSecret={paymentIntent.client_secret}
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                  amount={total}
                  loading={loading}
                />
              </StripeProvider>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
