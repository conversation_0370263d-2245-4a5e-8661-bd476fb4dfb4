import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ points: 0 })
    }

    // Get user's total points
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('total_points')
      .eq('id', user.id)
      .single()

    if (userError || !userData) {
      return NextResponse.json({ points: 0 })
    }

    return NextResponse.json({ points: userData.total_points || 0 })
  } catch (error) {
    console.error('Error fetching user points:', error)
    return NextResponse.json({ points: 0 })
  }
}
