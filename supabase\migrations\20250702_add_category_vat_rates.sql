-- Add category-based VAT rates management
-- Migration: 20250702_add_category_vat_rates

-- Create category VAT rates table
CREATE TABLE category_vat_rates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category product_category NOT NULL UNIQUE,
    vat_rate DECIMAL(5,4) NOT NULL DEFAULT 0.077,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert default VAT rates for existing categories
INSERT INTO category_vat_rates (category, vat_rate, is_active) VALUES
('coffee', 0.077, true),      -- 7.7% Swiss VAT for coffee
('accessories', 0.077, true); -- 7.7% Swiss VAT for accessories

-- Add indexes for performance
CREATE INDEX idx_category_vat_rates_category ON category_vat_rates(category);
CREATE INDEX idx_category_vat_rates_active ON category_vat_rates(is_active);

-- Add RLS policy for category VAT rates
ALTER TABLE category_vat_rates ENABLE ROW LEVEL SECURITY;

-- Public can read active VAT rates
CREATE POLICY "Anyone can view active category VAT rates" ON category_vat_rates
    FOR SELECT USING (is_active = true);

-- Only admins can manage VAT rates
CREATE POLICY "Admins can manage category VAT rates" ON category_vat_rates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_category_vat_rates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_category_vat_rates_updated_at
    BEFORE UPDATE ON category_vat_rates
    FOR EACH ROW
    EXECUTE FUNCTION update_category_vat_rates_updated_at();

-- Add comment for documentation
COMMENT ON TABLE category_vat_rates IS 'Category-specific VAT rates for products';
COMMENT ON COLUMN category_vat_rates.category IS 'Product category (coffee, accessories)';
COMMENT ON COLUMN category_vat_rates.vat_rate IS 'VAT rate as decimal (e.g., 0.077 for 7.7%)';
COMMENT ON COLUMN category_vat_rates.is_active IS 'Whether this VAT rate is currently active';
