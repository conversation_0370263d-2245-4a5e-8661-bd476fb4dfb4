import { createClient } from '@/lib/supabase/server'
import { notFound } from 'next/navigation'
import { ProductPageClient } from '@/components/product/product-page-client'

export const dynamic = 'force-dynamic'



interface ProductPageProps {
  params: Promise<{ locale: string; id: string }>
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { locale, id } = await params
  const supabase = await createClient()

  const { data: product, error } = await supabase
    .from('products')
    .select('*')
    .eq('id', id)
    .eq('is_available', true)
    .single()

  if (error || !product) {
    notFound()
  }

  return <ProductPageClient product={product} locale={locale} />
}
