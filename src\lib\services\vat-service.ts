// VAT service for managing category-based VAT rates

import { createClient } from '@/lib/supabase/client'
import type { CategoryVATRate, CategoryVATConfig, ProductCategory } from '@/lib/types/vat'

export class VATService {
  private supabase

  constructor() {
    this.supabase = createClient()
  }

  /**
   * Get all active category VAT rates
   */
  async getCategoryVATRates(): Promise<CategoryVATRate[]> {
    const { data, error } = await this.supabase
      .from('category_vat_rates')
      .select('*')
      .eq('is_active', true)
      .order('category')

    if (error) {
      console.error('Error fetching category VAT rates:', error)
      throw new Error('Failed to fetch VAT rates')
    }

    return data || []
  }

  /**
   * Get VAT rates as a simple category -> rate mapping
   */
  async getCategoryVATConfig(): Promise<CategoryVATConfig> {
    const rates = await this.getCategoryVATRates()
    const config: CategoryVATConfig = {}
    
    rates.forEach(rate => {
      config[rate.category] = rate.vat_rate
    })

    return config
  }

  /**
   * Get VAT rate for a specific category
   */
  async getVATRateForCategory(category: ProductCategory): Promise<number> {
    const { data, error } = await this.supabase
      .from('category_vat_rates')
      .select('vat_rate')
      .eq('category', category)
      .eq('is_active', true)
      .single()

    if (error) {
      console.error(`Error fetching VAT rate for category ${category}:`, error)
      // Return default Swiss VAT rate as fallback
      return 0.077
    }

    return data?.vat_rate || 0.077
  }

  /**
   * Update VAT rate for a category (admin only)
   */
  async updateCategoryVATRate(category: ProductCategory, vatRate: number): Promise<void> {
    const { error } = await this.supabase
      .from('category_vat_rates')
      .update({ 
        vat_rate: vatRate,
        updated_at: new Date().toISOString()
      })
      .eq('category', category)

    if (error) {
      console.error(`Error updating VAT rate for category ${category}:`, error)
      throw new Error(`Failed to update VAT rate for ${category}`)
    }
  }

  /**
   * Create or update multiple category VAT rates (admin only)
   */
  async updateMultipleCategoryVATRates(rates: { category: ProductCategory; vat_rate: number }[]): Promise<void> {
    const updates = rates.map(rate => ({
      category: rate.category,
      vat_rate: rate.vat_rate,
      updated_at: new Date().toISOString()
    }))

    const { error } = await this.supabase
      .from('category_vat_rates')
      .upsert(updates, { 
        onConflict: 'category',
        ignoreDuplicates: false 
      })

    if (error) {
      console.error('Error updating multiple category VAT rates:', error)
      throw new Error('Failed to update category VAT rates')
    }
  }

  /**
   * Initialize default VAT rates if they don't exist
   */
  async initializeDefaultVATRates(): Promise<void> {
    const existingRates = await this.getCategoryVATRates()
    
    if (existingRates.length === 0) {
      const defaultRates = [
        { category: 'coffee' as ProductCategory, vat_rate: 0.077, is_active: true },
        { category: 'accessories' as ProductCategory, vat_rate: 0.077, is_active: true }
      ]

      const { error } = await this.supabase
        .from('category_vat_rates')
        .insert(defaultRates)

      if (error) {
        console.error('Error initializing default VAT rates:', error)
        throw new Error('Failed to initialize default VAT rates')
      }
    }
  }
}

// Singleton instance
export const vatService = new VATService()

// Helper function to get category VAT rates
export async function getCategoryVATRates(): Promise<CategoryVATConfig> {
  try {
    return await vatService.getCategoryVATConfig()
  } catch (error) {
    console.error('Failed to fetch category VAT rates, using defaults:', error)
    // Return default rates as fallback
    return {
      coffee: 0.077,
      accessories: 0.077
    }
  }
}
