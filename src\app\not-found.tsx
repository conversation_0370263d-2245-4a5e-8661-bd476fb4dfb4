import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Coffee, Home, Search, Zap } from 'lucide-react'
import Link from 'next/link'
import { BackButton } from '@/components/ui/back-button'

export const dynamic = 'force-dynamic';

export default function NotFound() {
  return (
    <div className="min-h-[80vh] flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        <Card className="border-2 border-dashed border-amber-200">
          <CardContent className="p-12">
            {/* Fun Coffee Animation */}
            <div className="relative mb-8">
              <div className="flex justify-center items-center space-x-2">
                <Coffee className="h-16 w-16 text-amber-600 animate-bounce" />
                <div className="text-6xl font-bold text-amber-600">4</div>
                <Coffee className="h-16 w-16 text-amber-600 animate-bounce delay-100" />
                <div className="text-6xl font-bold text-amber-600">4</div>
              </div>
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-amber-400 rounded-full animate-ping"></div>
                  <div className="w-1 h-1 bg-amber-400 rounded-full animate-ping delay-75"></div>
                  <div className="w-1 h-1 bg-amber-400 rounded-full animate-ping delay-150"></div>
                </div>
              </div>
            </div>

            {/* Fun Headlines */}
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Oops! Diese Seite ist wie ein perfekter Espresso...
            </h1>
            <p className="text-xl text-amber-600 font-semibold mb-6">
              ...leider nicht mehr da! ☕
            </p>

            {/* Witty Description */}
            <div className="bg-amber-50 rounded-lg p-6 mb-8">
              <p className="text-gray-700 mb-4">
                Es scheint, als hätten Sie eine Seite gefunden, die so selten ist wie
                ein Kaffee ohne Koffein. Keine Sorge – wir haben viele andere
                großartige Seiten für Sie!
              </p>
              <div className="flex items-center justify-center space-x-2 text-sm text-amber-700">
                <Zap className="h-4 w-4" />
                <span>Tipp: Probieren Sie unseren Coffee Box Builder!</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  asChild
                >
                  <Link href="/de">
                    <Home className="mr-2 h-5 w-5" />
                    Zur Startseite
                  </Link>
                </Button>

                <Button
                  size="lg"
                  variant="outline"
                  asChild
                >
                  <Link href="/de/coffee-box-builder">
                    <Coffee className="mr-2 h-5 w-5" />
                    Coffee Box Builder
                  </Link>
                </Button>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <BackButton />

                <Button
                  variant="outline"
                  className="border-gray-300"
                  asChild
                >
                  <Link href="/de/shop">
                    <Search className="mr-2 h-4 w-4" />
                    Shop durchsuchen
                  </Link>
                </Button>
              </div>
            </div>

            {/* Fun Footer */}
            <div className="mt-8 pt-6 border-t border-amber-200">
              <p className="text-sm text-gray-500">
                Fehlercode: 404 | Status: Kaffee wird gebraucht ☕
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
