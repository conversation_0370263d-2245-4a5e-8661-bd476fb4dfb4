/**
 * @jest-environment jsdom
 */



// Mock the Supabase server client
const mockSupabaseClient = {
  auth: {
    getUser: jest.fn(),
  },
  from: jest.fn(),
}

jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn(() => Promise.resolve(mockSupabaseClient)),
}))

// Import the API handler after mocking
import { GET as getUserGifts } from '@/app/api/user/gifts/route'
import { GET as getUserPoints } from '@/app/api/user/points/route'

describe('Gift Points API System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/user/points', () => {
    it('should return user points when authenticated', async () => {
      // Mock authenticated user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null,
      })

      // Mock user data with points
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { total_points: 250 },
              error: null,
            }),
          }),
        }),
      })

      const response = await getUserPoints()
      const data = await response.json()

      expect(data.points).toBe(250)
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('users')
    })

    it('should return 0 points when user not authenticated', async () => {
      // Mock unauthenticated user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const response = await getUserPoints()
      const data = await response.json()

      expect(data.points).toBe(0)
    })

    it('should return 0 points when user has no points data', async () => {
      // Mock authenticated user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null,
      })

      // Mock user data with no points
      mockSupabaseClient.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: null,
            }),
          }),
        }),
      })

      const response = await getUserPoints()
      const data = await response.json()

      expect(data.points).toBe(0)
    })
  })

  describe('GET /api/user/gifts', () => {
    it('should return gifts based on user points', async () => {
      // Mock authenticated user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null,
      })

      const mockGifts = [
        { id: '1', threshold_points: 100, gift_product_ids: ['product1'], is_active: true },
        { id: '2', threshold_points: 200, gift_product_ids: ['product2'], is_active: true },
      ]

      const mockNextGift = {
        id: '3',
        threshold_points: 300,
        gift_product_ids: ['product3'],
        is_active: true,
      }

      // Mock the from calls in sequence
      let callCount = 0
      mockSupabaseClient.from.mockImplementation((table) => {
        callCount++
        
        if (callCount === 1 && table === 'users') {
          // First call: get user points
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: { total_points: 250 },
                  error: null,
                }),
              }),
            }),
          }
        } else if (callCount === 2 && table === 'gift_thresholds') {
          // Second call: get applicable gifts
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                lte: jest.fn().mockReturnValue({
                  order: jest.fn().mockResolvedValue({
                    data: mockGifts,
                    error: null,
                  }),
                }),
              }),
            }),
          }
        } else if (callCount === 3 && table === 'gift_thresholds') {
          // Third call: get next gift threshold
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                gt: jest.fn().mockReturnValue({
                  order: jest.fn().mockReturnValue({
                    limit: jest.fn().mockResolvedValue({
                      data: [mockNextGift],
                      error: null,
                    }),
                  }),
                }),
              }),
            }),
          }
        }
        
        return {
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({ data: null, error: null }),
            }),
          }),
        }
      })

      const response = await getUserGifts()
      const data = await response.json()

      expect(data.userPoints).toBe(250)
      expect(data.applicableGifts).toHaveLength(2)
      expect(data.applicableGifts[0].threshold_points).toBe(100)
      expect(data.applicableGifts[1].threshold_points).toBe(200)
      expect(data.nextGiftThreshold.threshold_points).toBe(300)
    })

    it('should return empty data when user not authenticated', async () => {
      // Mock unauthenticated user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      })

      const response = await getUserGifts()
      const data = await response.json()

      expect(data.userPoints).toBe(0)
      expect(data.applicableGifts).toHaveLength(0)
      expect(data.nextGiftThreshold).toBeNull()
    })
  })

  describe('Points-based System Verification', () => {
    it('should query gift_thresholds with threshold_points field', async () => {
      // Mock authenticated user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-123' } },
        error: null,
      })

      const mockLte = jest.fn().mockReturnValue({
        order: jest.fn().mockResolvedValue({
          data: [],
          error: null,
        }),
      })

      const mockGt = jest.fn().mockReturnValue({
        order: jest.fn().mockReturnValue({
          limit: jest.fn().mockResolvedValue({
            data: [],
            error: null,
          }),
        }),
      })

      let callCount = 0
      mockSupabaseClient.from.mockImplementation((table) => {
        callCount++
        
        if (callCount === 1 && table === 'users') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest.fn().mockResolvedValue({
                  data: { total_points: 150 },
                  error: null,
                }),
              }),
            }),
          }
        } else if (table === 'gift_thresholds') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                lte: mockLte,
                gt: mockGt,
              }),
            }),
          }
        }
        
        return { select: jest.fn() }
      })

      await getUserGifts()

      // Verify that the queries use threshold_points, not threshold_amount
      expect(mockLte).toHaveBeenCalledWith('threshold_points', 150)
      expect(mockGt).toHaveBeenCalledWith('threshold_points', 150)
    })
  })
})
