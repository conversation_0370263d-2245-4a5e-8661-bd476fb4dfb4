import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ 
        applicableGifts: [], 
        nextGiftThreshold: null,
        userPoints: 0 
      })
    }

    // Get user's total points
    const { data: userData } = await supabase
      .from('users')
      .select('total_points')
      .eq('id', user.id)
      .single()

    const userPoints = userData?.total_points || 0

    // Get applicable gift thresholds
    const { data: applicableGifts } = await supabase
      .from('gift_thresholds')
      .select('*')
      .eq('is_active', true)
      .lte('threshold_points', userPoints)
      .order('threshold_points', { ascending: false })

    // Get next gift threshold
    const { data: nextGiftData } = await supabase
      .from('gift_thresholds')
      .select('*')
      .eq('is_active', true)
      .gt('threshold_points', userPoints)
      .order('threshold_points', { ascending: true })
      .limit(1)

    return NextResponse.json({
      applicableGifts: applicableGifts || [],
      nextGiftThreshold: nextGiftData?.[0] || null,
      userPoints
    })
  } catch (error) {
    console.error('Error fetching user gifts:', error)
    return NextResponse.json({ 
      applicableGifts: [], 
      nextGiftThreshold: null,
      userPoints: 0 
    })
  }
}
