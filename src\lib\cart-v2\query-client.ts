// Cart V2 Query Client Configuration
// TanStack Query client setup with cart-specific optimizations

import { QueryClient } from '@tanstack/react-query'
import { CART_CONFIG } from './types'

/**
 * Create a new QueryClient instance with cart-optimized settings
 */
export function createCartQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Cart data is relatively stable, so we can cache it longer
        staleTime: CART_CONFIG.STALE_TIME, // 30 seconds
        gcTime: CART_CONFIG.CACHE_TIME, // 5 minutes
        
        // Don't refetch on window focus for cart data
        refetchOnWindowFocus: false,
        
        // Retry failed requests with exponential backoff
        retry: (failureCount, error: unknown) => {
          // Don't retry on client errors (4xx)
          const errorWithStatus = error as { status?: number }
          if (errorWithStatus?.status && errorWithStatus.status >= 400 && errorWithStatus.status < 500) {
            return false
          }
          // Retry up to 3 times for server errors
          return failureCount < 3
        },
        
        // Retry delay with exponential backoff
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
      
      mutations: {
        // Retry mutations once on network errors
        retry: (failureCount, error: unknown) => {
          // Only retry network errors, not business logic errors
          const errorWithCode = error as { code?: string }
          if (errorWithCode?.code && errorWithCode.code !== 'NETWORK_ERROR') {
            return false
          }
          return failureCount < 1
        },
        
        // Shorter retry delay for mutations
        retryDelay: 1000,
      },
    },
  })
}

/**
 * Default query client instance for cart operations
 */
export const cartQueryClient = createCartQueryClient()

/**
 * Query client configuration for server-side rendering
 */
export function createSSRQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Shorter stale time for SSR to ensure fresh data
        staleTime: 0,
        gcTime: 1000 * 60 * 5, // 5 minutes
        
        // Don't retry on server
        retry: false,
        
        // Don't refetch on client hydration
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
      },
    },
  })
}

/**
 * Prefetch cart data for better performance
 */
export async function prefetchCartData(
  queryClient: QueryClient,
  userId?: string
): Promise<void> {
  const queryKey = userId 
    ? ['cart', 'user', userId] 
    : ['cart', 'session']

  // Only prefetch if data is not already cached
  const existingData = queryClient.getQueryData(queryKey)
  if (existingData) return

  try {
    const { CartService } = await import('./service')
    const cartService = new CartService()
    
    await queryClient.prefetchQuery({
      queryKey,
      queryFn: () => cartService.getCart(),
      staleTime: CART_CONFIG.STALE_TIME,
    })
  } catch (error) {
    // Silently fail prefetch - the query will be retried when actually needed
    console.warn('Failed to prefetch cart data:', error)
  }
}

/**
 * Invalidate all cart-related queries
 */
export function invalidateAllCartQueries(queryClient: QueryClient): void {
  queryClient.invalidateQueries({
    queryKey: ['cart'],
  })
}

/**
 * Clear all cart-related cache
 */
export function clearCartCache(queryClient: QueryClient): void {
  queryClient.removeQueries({
    queryKey: ['cart'],
  })
}

/**
 * Get cached cart data without triggering a fetch
 */
export function getCachedCart(
  queryClient: QueryClient,
  userId?: string
) {
  const queryKey = userId 
    ? ['cart', 'user', userId] 
    : ['cart', 'session']

  return queryClient.getQueryData(queryKey)
}

/**
 * Set cart data in cache (useful for optimistic updates)
 */
export function setCachedCart(
  queryClient: QueryClient,
  data: unknown,
  userId?: string
): void {
  const queryKey = userId 
    ? ['cart', 'user', userId] 
    : ['cart', 'session']

  queryClient.setQueryData(queryKey, data)
}

/**
 * Query client error handler for cart operations
 */
export function handleCartQueryError(error: unknown): void {
  // Log error for monitoring
  console.error('Cart query error:', {
    message: (error as { message?: string }).message,
    code: (error as { code?: string }).code,
    stack: (error as { stack?: string }).stack,
    timestamp: new Date().toISOString(),
  })

  // You can integrate with error reporting services here
  // Example: Sentry, LogRocket, etc.
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', 'exception', {
      description: `Cart Error: ${(error as { message?: string }).message}`,
      fatal: false,
    })
  }
}

/**
 * Performance monitoring for cart queries
 */
export function setupCartQueryMonitoring(queryClient: QueryClient): void {
  // Monitor query performance
  queryClient.getQueryCache().subscribe((event) => {
    if (event.type === 'updated' && event.query.queryKey[0] === 'cart') {
      const { state } = event.query
      
      if (state.status === 'success' && state.dataUpdatedAt) {
        const duration = state.dataUpdatedAt - (state.fetchFailureCount > 0 ? 0 : state.dataUpdatedAt)
        
        // Log slow queries (> 2 seconds)
        if (duration > 2000) {
          console.warn('Slow cart query detected:', {
            queryKey: event.query.queryKey,
            duration,
            timestamp: new Date().toISOString(),
          })
        }
      }
      
      if (state.status === 'error') {
        handleCartQueryError(state.error)
      }
    }
  })
}
