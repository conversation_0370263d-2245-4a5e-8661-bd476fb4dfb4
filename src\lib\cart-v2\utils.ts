// Cart V2 Utilities
// Helper functions for cart calculations and transformations

import type { Cart, CartItem, CartSummary, CartProduct } from './types'
import { CART_CONFIG } from './types'

/**
 * Calculate cart summary including totals, shipping, and discounts
 */
export function calculateCartSummary(cart: Cart | null): CartSummary {
  if (!cart || !cart.items.length) {
    return {
      subtotal: 0,
      shipping_cost: 0,
      tax_amount: 0,
      discount_amount: 0,
      total: 0,
      items_count: 0,
      free_shipping_threshold: CART_CONFIG.FREE_SHIPPING_THRESHOLD,
      free_shipping_remaining: CART_CONFIG.FREE_SHIPPING_THRESHOLD,
    }
  }

  // Calculate subtotal (prices are VAT-inclusive)
  const subtotal = cart.items.reduce((sum, item) => {
    if (!item.product) return sum
    const price = item.product.discount_price || item.product.price
    return sum + (price * item.quantity)
  }, 0)

  // Calculate shipping cost
  const shipping_cost = subtotal >= CART_CONFIG.FREE_SHIPPING_THRESHOLD 
    ? 0 
    : CART_CONFIG.DEFAULT_SHIPPING_COST

  // Calculate free shipping remaining
  const free_shipping_remaining = Math.max(
    0, 
    CART_CONFIG.FREE_SHIPPING_THRESHOLD - subtotal
  )

  // Count total items
  const items_count = cart.items.reduce((sum, item) => sum + item.quantity, 0)

  // For Swiss VAT system, prices are inclusive, so tax is calculated from total
  // This is handled at checkout level, not cart level
  const tax_amount = 0
  const discount_amount = 0 // Will be handled by coupon system

  const total = subtotal + shipping_cost - discount_amount

  return {
    subtotal,
    shipping_cost,
    tax_amount,
    discount_amount,
    total,
    items_count,
    free_shipping_threshold: CART_CONFIG.FREE_SHIPPING_THRESHOLD,
    free_shipping_remaining,
  }
}

/**
 * Get effective price for a product (discount price if available, otherwise regular price)
 */
export function getEffectivePrice(product: CartProduct): number {
  return product.discount_price || product.price
}

/**
 * Calculate line total for a cart item
 */
export function calculateLineTotal(item: CartItem): number {
  if (!item.product) return 0
  return getEffectivePrice(item.product) * item.quantity
}

/**
 * Check if a product has a discount
 */
export function hasDiscount(product: CartProduct): boolean {
  return !!(product.discount_price && product.discount_price < product.price)
}

/**
 * Calculate discount amount for a product
 */
export function getDiscountAmount(product: CartProduct): number {
  if (!hasDiscount(product)) return 0
  return product.price - (product.discount_price || product.price)
}

/**
 * Calculate discount percentage for a product
 */
export function getDiscountPercentage(product: CartProduct): number {
  if (!hasDiscount(product)) return 0
  const discount = getDiscountAmount(product)
  return Math.round((discount / product.price) * 100)
}

/**
 * Transform database cart data to Cart interface
 */
export function transformCartData(data: unknown): Cart {
  const cartData = data as Record<string, unknown>
  return {
    id: cartData.id as string,
    user_id: (cartData.user_id as string | null) || undefined,
    session_id: (cartData.session_id as string | null) || undefined,
    status: cartData.status as 'active' | 'abandoned' | 'converted',
    total_amount: parseFloat((cartData.total_amount as string) || '0'),
    items: (cartData.cart_items as unknown[])?.map(transformCartItemData) || [],
    created_at: cartData.created_at as string,
    updated_at: cartData.updated_at as string,
  }
}

/**
 * Transform database cart item data to CartItem interface
 */
export function transformCartItemData(data: unknown): CartItem {
  const itemData = data as Record<string, unknown>
  return {
    id: itemData.id as string,
    cart_id: itemData.cart_id as string,
    product_id: itemData.product_id as string,
    quantity: itemData.quantity as number,
    created_at: itemData.created_at as string,
    updated_at: itemData.updated_at as string,
    product: itemData.products ? transformProductData(itemData.products) : undefined,
  }
}

/**
 * Transform database product data to CartProduct interface
 */
export function transformProductData(data: unknown): CartProduct {
  const productData = data as Record<string, unknown>
  return {
    id: productData.id as string,
    title: productData.title as string,
    price: parseFloat(productData.price as string),
    discount_price: productData.discount_price ? parseFloat(productData.discount_price as string) : undefined,
    images: (productData.images as string[]) || [],
    category: productData.category as string,
    coffee_type: (productData.coffee_type as string | null) || undefined,
    brand: (productData.brand as string | null) || undefined,
    slug: productData.slug as string,
    is_available: (productData.is_available as boolean) ?? true,
  }
}

/**
 * Generate session ID for guest users
 */
export function generateSessionId(): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).slice(2, 9)
  return `sess_${timestamp}_${random}`
}

/**
 * Get session ID from cookies (client-side)
 */
export function getClientSessionId(): string {
  if (typeof document === 'undefined') return 'server-session'
  
  const match = document.cookie.match(
    new RegExp(`(?:^|; )${CART_CONFIG.SESSION_COOKIE_NAME}=([^;]*)`)
  )
  
  if (match?.[1]) return match[1]
  
  const sessionId = generateSessionId()
  document.cookie = `${CART_CONFIG.SESSION_COOKIE_NAME}=${sessionId}; path=/; max-age=${CART_CONFIG.SESSION_MAX_AGE}`
  return sessionId
}

/**
 * Clear session cookie (client-side)
 */
export function clearClientSession(): void {
  if (typeof document === 'undefined') return
  document.cookie = `${CART_CONFIG.SESSION_COOKIE_NAME}=; path=/; max-age=0`
}

/**
 * Validate quantity value
 */
export function validateQuantity(quantity: number): boolean {
  return Number.isInteger(quantity) && quantity > 0 && quantity <= 999
}

/**
 * Sanitize quantity value
 */
export function sanitizeQuantity(quantity: number): number {
  const sanitized = Math.floor(Math.abs(quantity))
  return Math.min(Math.max(sanitized, 1), 999)
}

/**
 * Check if cart is empty
 */
export function isCartEmpty(cart: Cart | null): boolean {
  return !cart || !cart.items.length
}

/**
 * Find cart item by product ID
 */
export function findCartItemByProductId(cart: Cart, productId: string): CartItem | undefined {
  return cart.items.find(item => item.product_id === productId)
}

/**
 * Get total quantity for a specific product in cart
 */
export function getProductQuantityInCart(cart: Cart, productId: string): number {
  const item = findCartItemByProductId(cart, productId)
  return item?.quantity || 0
}

/**
 * Create optimistic cart item for immediate UI updates
 */
export function createOptimisticCartItem(
  productId: string,
  quantity: number,
  product?: CartProduct
): CartItem {
  return {
    id: `optimistic_${Date.now()}_${Math.random().toString(36).slice(2)}`,
    cart_id: 'optimistic',
    product_id: productId,
    quantity,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    product,
  }
}
