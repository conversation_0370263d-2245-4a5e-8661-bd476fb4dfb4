import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { sendOrderConfirmationEmail, sendAdminOrderNotificationEmail } from '@/lib/email'

interface OrderItem {
  product_id: string
  quantity: number
  unit_price: number
  total_price: number
}

interface Customer {
  id?: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  isGuest: boolean
}

interface Address {
  first_name: string
  last_name: string
  street_address: string
  city: string
  postal_code: string
  country: string
}

interface Coupon {
  id: string
  code: string
  discountAmount: number
}

interface OrderData {
  customer: Customer
  items: OrderItem[]
  shippingAddress: Address
  billingAddress: Address
  subtotal: number
  shippingCost: number
  taxAmount: number
  discountAmount: number
  total: number
  coupon?: Coupon | null
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin status
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (profileError || !profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Parse request body
    const orderData: OrderData = await request.json()

    // Validate required fields
    if (!orderData.customer || !orderData.items || orderData.items.length === 0) {
      return NextResponse.json({ error: 'Missing required order data' }, { status: 400 })
    }

    if (!orderData.shippingAddress || !orderData.billingAddress) {
      return NextResponse.json({ error: 'Missing address information' }, { status: 400 })
    }

    // Use admin client to bypass RLS (already imported)

    // Handle customer creation/selection
    let customerId: string | null = null

    if (orderData.customer.isGuest) {
      // Check if user already exists with this email
      const { data: existingUser } = await supabaseAdmin
        .from('users')
        .select('id')
        .eq('email', orderData.customer.email)
        .single()

      if (existingUser) {
        customerId = existingUser.id
      } else {
        // Create guest user account
        const { data: newUser, error: userError } = await supabaseAdmin
          .from('users')
          .insert({
            email: orderData.customer.email,
            first_name: orderData.customer.firstName,
            last_name: orderData.customer.lastName,
            phone: orderData.customer.phone,
            is_guest: true,
            email_verified: false
          })
          .select('id')
          .single()

        if (userError) {
          console.error('Error creating guest user:', userError)
          return NextResponse.json({ error: 'Failed to create customer' }, { status: 500 })
        }

        customerId = newUser.id
      }
    } else {
      customerId = orderData.customer.id!
    }

    // Create the order
    const { data: order, error: orderError } = await supabaseAdmin
      .from('orders')
      .insert({
        user_id: customerId,
        total_amount: orderData.total,
        subtotal_amount: orderData.subtotal,
        shipping_amount: orderData.shippingCost,
        tax_amount: orderData.taxAmount,
        discount_amount: orderData.discountAmount,
        payment_status: 'paid', // Manual orders are marked as paid
        order_status: 'confirmed', // Start with confirmed status
        shipping_address: {
          firstName: orderData.shippingAddress.first_name,
          lastName: orderData.shippingAddress.last_name,
          street: orderData.shippingAddress.street_address,
          city: orderData.shippingAddress.city,
          postalCode: orderData.shippingAddress.postal_code,
          country: orderData.shippingAddress.country
        },
        billing_address: {
          firstName: orderData.billingAddress.first_name,
          lastName: orderData.billingAddress.last_name,
          street: orderData.billingAddress.street_address,
          city: orderData.billingAddress.city,
          postalCode: orderData.billingAddress.postal_code,
          country: orderData.billingAddress.country
        },
        coupon_id: orderData.coupon?.id || null,
        coupon_code: orderData.coupon?.code || null,
        created_by_admin: user.id
      })
      .select('id, order_number')
      .single()

    if (orderError) {
      console.error('Error creating order:', orderError)
      return NextResponse.json({ error: 'Failed to create order' }, { status: 500 })
    }

    // Create order items
    const orderItems = orderData.items.map(item => ({
      order_id: order.id,
      product_id: item.product_id,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.total_price
    }))

    const { error: itemsError } = await supabaseAdmin
      .from('order_items')
      .insert(orderItems)

    if (itemsError) {
      console.error('Error creating order items:', itemsError)
      // Rollback order creation
      await supabaseAdmin.from('orders').delete().eq('id', order.id)
      return NextResponse.json({ error: 'Failed to create order items' }, { status: 500 })
    }

    // Update product inventory
    for (const item of orderData.items) {
      // Get current inventory
      const { data: product } = await supabaseAdmin
        .from('products')
        .select('inventory_count')
        .eq('id', item.product_id)
        .single()

      if (product) {
        const newInventory = Math.max(0, product.inventory_count - item.quantity)
        const { error: inventoryError } = await supabaseAdmin
          .from('products')
          .update({ inventory_count: newInventory })
          .eq('id', item.product_id)

        if (inventoryError) {
          console.error('Error updating inventory:', inventoryError)
          // Continue with order creation even if inventory update fails
        }
      }
    }

    // Update coupon usage if applicable
    if (orderData.coupon) {
      // Get current usage count
      const { data: currentCoupon } = await supabaseAdmin
        .from('coupons')
        .select('used_count')
        .eq('id', orderData.coupon.id)
        .single()

      const { error: couponError } = await supabaseAdmin
        .from('coupons')
        .update({
          used_count: (currentCoupon?.used_count || 0) + 1
        })
        .eq('id', orderData.coupon.id)

      if (couponError) {
        console.error('Error updating coupon usage:', couponError)
        // Continue with order creation even if coupon update fails
      }
    }

    // Get complete order data for emails
    const { data: completeOrder, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        *,
        users!inner(first_name, last_name, email),
        order_items(
          *,
          products(title, brand, category)
        )
      `)
      .eq('id', order.id)
      .single()

    if (fetchError) {
      console.error('Error fetching complete order:', fetchError)
    } else {
      // Send email notifications
      try {
        await sendOrderConfirmationEmail(completeOrder)
        await sendAdminOrderNotificationEmail(completeOrder)
      } catch (emailError) {
        console.error('Error sending emails:', emailError)
        // Don't fail the order creation if emails fail
      }
    }

    return NextResponse.json({
      success: true,
      orderId: order.id,
      orderNumber: order.order_number,
      message: 'Manual order created successfully'
    })

  } catch (error) {
    console.error('Error in manual order creation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
