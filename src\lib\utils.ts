import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Currency formatting for Swiss market (CHF)
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('de-CH', {
    style: 'currency',
    currency: 'CHF'
  }).format(amount)
}

// Date formatting for Swiss market (dd/mm/yyyy)
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('de-CH', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date)
}

// DateTime formatting for Swiss market
export function formatDateTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('de-CH', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj)
}

// Calculate cost per espresso (for coffee products)
export function calculateCostPerEspresso(price: number, servings: number): number {
  if (servings <= 0) return 0
  return price / servings
}

// Generate URL-friendly slug
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
}

// Email validation
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Swiss postal code validation
export function isValidSwissPostalCode(postalCode: string): boolean {
  const swissPostalRegex = /^[1-9]\d{3}$/
  return swissPostalRegex.test(postalCode)
}

// Calculate discount percentage
export function calculateDiscountPercentage(originalPrice: number, discountedPrice: number): number {
  if (originalPrice <= 0) return 0
  return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100)
}

// Calculate points earned (1 CHF = 10 points)
export function calculatePointsEarned(amount: number): number {
  return Math.floor(amount * 10)
}

// Determine user level based on points
export function determineUserLevel(points: number): string {
  if (points >= 10000) return 'Platinum'
  if (points >= 5000) return 'Gold'
  if (points >= 2000) return 'Silver'
  return 'Bronze'
}

// Calculate shipping cost based on Swiss rates
export function calculateShippingCost(subtotal: number): number {
  // Free shipping over 90 CHF
  if (subtotal >= 90) return 0

  // Standard shipping rate
  return 8.50
}

// Generate coupon code
export function generateCouponCode(prefix: string = 'PC'): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = prefix
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// Validate coupon code format
export function isValidCouponCode(code: string): boolean {
  return /^[A-Z0-9]{4,12}$/.test(code)
}

// Swiss VAT calculations (7.7% standard rate)
const DEFAULT_VAT_RATE = 0.077

export function calculateVATFromInclusive(inclusiveAmount: number, vatRate: number = DEFAULT_VAT_RATE): number {
  return inclusiveAmount - (inclusiveAmount / (1 + vatRate))
}

export function calculateExclusiveFromInclusive(inclusiveAmount: number, vatRate: number = DEFAULT_VAT_RATE): number {
  return inclusiveAmount / (1 + vatRate)
}

export function calculateInclusiveFromExclusive(exclusiveAmount: number, vatRate: number = DEFAULT_VAT_RATE): number {
  return exclusiveAmount * (1 + vatRate)
}

export interface VATBreakdown {
  inclusive: number
  exclusive: number
  vatAmount: number
  vatRate: number
}

export function formatVATBreakdown(inclusiveAmount: number, vatRate: number = DEFAULT_VAT_RATE): VATBreakdown {
  const exclusive = calculateExclusiveFromInclusive(inclusiveAmount, vatRate)
  const vatAmount = calculateVATFromInclusive(inclusiveAmount, vatRate)

  return {
    inclusive: Math.round(inclusiveAmount * 100) / 100,
    exclusive: Math.round(exclusive * 100) / 100,
    vatAmount: Math.round(vatAmount * 100) / 100,
    vatRate
  }
}
