'use client'

import { useEffect, useState, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AdminBackButton } from '@/components/admin/admin-back-button'
import { ArrowLeft, Plus } from 'lucide-react'
import Link from 'next/link'
import { useToast } from '@/hooks/use-toast'
import { CustomerSelectionSection } from '@/components/admin/manual-order/customer-selection'
import { ProductSelectionSection } from '@/components/admin/manual-order/product-selection'
import { ShippingBillingSection } from '@/components/admin/manual-order/shipping-billing'
import { OrderSummarySection } from '@/components/admin/manual-order/order-summary'
import type { Product, UserAddress } from '@/types'

interface SelectedCustomer {
  id?: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  isGuest: boolean
}

interface OrderItem {
  product: Product
  quantity: number
  unitPrice: number
  totalPrice: number
}

interface AppliedCoupon {
  id: string
  code: string
  type: 'percentage' | 'fixed_amount'
  value: number
  discountAmount: number
}

export default function CreateManualOrderPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])
  const { toast } = useToast()

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [creating, setCreating] = useState(false)

  // Order state
  const [selectedCustomer, setSelectedCustomer] = useState<SelectedCustomer | null>(null)
  const [orderItems, setOrderItems] = useState<OrderItem[]>([])
  const [shippingAddress, setShippingAddress] = useState<UserAddress | null>(null)
  const [billingAddress, setBillingAddress] = useState<UserAddress | null>(null)
  const [billingSameAsShipping, setBillingSameAsShipping] = useState(true)
  const [appliedCoupon, setAppliedCoupon] = useState<AppliedCoupon | null>(null)

  // Calculations
  const subtotal = orderItems.reduce((sum, item) => sum + item.totalPrice, 0)
  const shippingCost = subtotal >= 90 ? 0 : 9.90 // Free shipping over CHF 90
  const taxAmount = 0 // No tax for Swiss market
  const discountAmount = appliedCoupon?.discountAmount || 0
  const total = subtotal + shippingCost + taxAmount - discountAmount

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return

        console.log('Checking authentication...')
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.email)

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading page')
        setAuthChecked(true)
        setLoading(false)
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase])

  const handleCreateOrder = async () => {
    // Validation
    if (!selectedCustomer) {
      toast({
        title: t('manualOrderPage.messages.createError'),
        description: t('manualOrderPage.messages.customerRequired'),
        variant: 'destructive'
      })
      return
    }

    if (orderItems.length === 0) {
      toast({
        title: t('manualOrderPage.messages.createError'),
        description: t('manualOrderPage.messages.productsRequired'),
        variant: 'destructive'
      })
      return
    }

    if (!shippingAddress) {
      toast({
        title: t('manualOrderPage.messages.createError'),
        description: t('manualOrderPage.messages.addressRequired'),
        variant: 'destructive'
      })
      return
    }

    setCreating(true)

    try {
      const orderData = {
        customer: selectedCustomer,
        items: orderItems.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          total_price: item.totalPrice
        })),
        shippingAddress,
        billingAddress: billingSameAsShipping ? shippingAddress : billingAddress,
        subtotal,
        shippingCost,
        taxAmount,
        discountAmount,
        total,
        coupon: appliedCoupon ? {
          id: appliedCoupon.id,
          code: appliedCoupon.code,
          discountAmount: appliedCoupon.discountAmount
        } : null
      }

      const response = await fetch('/api/admin/orders/create-manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: t('manualOrderPage.messages.orderCreated'),
          description: t('manualOrderPage.messages.orderCreatedDesc'),
        })
        router.push(`/${locale}/admin/orders/${result.orderId}`)
      } else {
        const error = await response.json()
        throw new Error(error.error || t('manualOrderPage.messages.createError'))
      }
    } catch (error) {
      console.error('Error creating manual order:', error)
      toast({
        title: t('manualOrderPage.messages.createError'),
        description: error instanceof Error ? error.message : t('manualOrderPage.messages.createError'),
        variant: 'destructive'
      })
    } finally {
      setCreating(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/admin/orders`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{t('manualOrderPage.title')}</h1>
          <p className="text-muted-foreground">
            {t('manualOrderPage.subtitle')}
          </p>
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Left Column - Customer and Products */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Selection */}
          <CustomerSelectionSection
            selectedCustomer={selectedCustomer}
            onCustomerSelect={setSelectedCustomer}
          />

          {/* Product Selection */}
          <ProductSelectionSection
            orderItems={orderItems}
            onItemsChange={setOrderItems}
          />

          {/* Shipping & Billing */}
          <ShippingBillingSection
            shippingAddress={shippingAddress}
            billingAddress={billingAddress}
            billingSameAsShipping={billingSameAsShipping}
            onShippingAddressChange={setShippingAddress}
            onBillingAddressChange={setBillingAddress}
            onBillingSameAsShippingChange={setBillingSameAsShipping}
          />
        </div>

        {/* Right Column - Order Summary */}
        <div className="space-y-6">
          <OrderSummarySection
            subtotal={subtotal}
            shippingCost={shippingCost}
            taxAmount={taxAmount}
            discountAmount={discountAmount}
            total={total}
            appliedCoupon={appliedCoupon}
            onCouponChange={setAppliedCoupon}
          />

          {/* Actions */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-3">
                <Button
                  onClick={handleCreateOrder}
                  disabled={creating || !selectedCustomer || orderItems.length === 0 || !shippingAddress}
                  className="w-full"
                  size="lg"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  {creating ? t('manualOrderPage.actions.creating') : t('manualOrderPage.actions.createOrder')}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push(`/${locale}/admin/orders`)}
                  className="w-full"
                >
                  {t('manualOrderPage.actions.cancel')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
