import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getUserLevel } from '@/lib/gamification';

export async function GET() {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('total_points, lifetime_spend')
      .eq('id', user.id)
      .single();
    if (profileError || !profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    const userPoints = profile.total_points || 0;

    // Get user level information based on total points
    const currentLevel = await getUserLevel(userPoints);

    // Get all levels for progress display
    const { data: allLevels } = await supabase
      .from('user_levels')
      .select('*')
      .order('level', { ascending: true });

    // Get next level information
    const { data: nextLevel } = await supabase
      .from('user_levels')
      .select('*')
      .gt('minimum_points', userPoints)
      .order('minimum_points', { ascending: true })
      .limit(1)
      .single();

    // Get applicable gift thresholds (already earned)
    const { data: applicableGifts } = await supabase
      .from('gift_thresholds')
      .select('*')
      .eq('is_active', true)
      .lte('threshold_points', userPoints)
      .order('threshold_points', { ascending: false });

    // Get next gift threshold
    const { data: nextGiftData } = await supabase
      .from('gift_thresholds')
      .select('*')
      .eq('is_active', true)
      .gt('threshold_points', userPoints)
      .order('threshold_points', { ascending: true })
      .limit(1);

    // Get all future gift thresholds for preview
    const { data: futureGifts } = await supabase
      .from('gift_thresholds')
      .select('*')
      .eq('is_active', true)
      .gt('threshold_points', userPoints)
      .order('threshold_points', { ascending: true });

    // Get product details for gifts
    const allGiftProductIds = [
      ...(applicableGifts || []).flatMap(g => g.gift_product_ids),
      ...(futureGifts || []).flatMap(g => g.gift_product_ids)
    ];

    const { data: giftProducts } = await supabase
      .from('products')
      .select('id, title, price, image_url, description')
      .in('id', allGiftProductIds);

    // Calculate points needed for next level
    const pointsToNext = nextLevel ? nextLevel.minimum_points - userPoints : 0;
    const progressToNext = nextLevel && currentLevel
      ? ((userPoints - currentLevel.minimum_points) / (nextLevel.minimum_points - currentLevel.minimum_points)) * 100
      : 100;

    return NextResponse.json({
      // Current status
      currentLevel: currentLevel?.level || 1,
      currentLevelName: currentLevel?.name || 'Bronze',
      totalPoints: userPoints,
      lifetimeSpend: profile.lifetime_spend || 0,

      // Progress information
      pointsToNext,
      nextLevelName: nextLevel?.name || null,
      progressToNext: Math.min(Math.max(progressToNext, 0), 100),

      // Current benefits
      discountPercent: currentLevel?.discount_percentage || 0,
      pointsMultiplier: currentLevel?.points_multiplier || 1,

      // All levels for overview
      allLevels: allLevels || [],

      // Gift information
      applicableGifts: applicableGifts || [],
      nextGiftThreshold: nextGiftData?.[0] || null,
      futureGifts: futureGifts || [],
      giftProducts: giftProducts || []
    });
  } catch (error) {
    console.error('Error in loyalty API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
