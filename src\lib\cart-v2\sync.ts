// Cart V2 Synchronization Logic
// Client-side utilities for cart synchronization between guest and authenticated users

import { CartService } from './service'
import { getClientCartSessionId, clearClientCartSession } from '../cart-session'
import { CartError } from './errors'
import type { Cart } from './types'

export interface SyncResult {
  success: boolean
  cart: Cart | null
  error?: string
  itemsTransferred?: number
  duplicatesHandled?: number
}

export class CartSyncManager {
  private cartService: CartService

  constructor() {
    this.cartService = new CartService()
  }

  /**
   * Sync guest cart to authenticated user after login
   * This should be called after successful authentication
   */
  async syncGuestCartToUser(): Promise<SyncResult> {
    try {
      const guestSessionId = getClientCartSessionId()
      
      // Skip if no valid guest session
      if (!guestSessionId || guestSessionId === 'server-session') {
        return {
          success: true,
          cart: await this.cartService.getCart(),
          itemsTransferred: 0,
          duplicatesHandled: 0
        }
      }

      console.log('🔄 Cart Sync: Starting guest cart synchronization', { guestSessionId })

      // Get guest cart before sync to count items
      const guestCart = await this.getGuestCartInfo(guestSessionId)
      const itemsToTransfer = guestCart?.items?.length || 0

      if (itemsToTransfer === 0) {
        console.log('🔄 Cart Sync: No items to transfer')
        clearClientCartSession()
        return {
          success: true,
          cart: await this.cartService.getCart(),
          itemsTransferred: 0,
          duplicatesHandled: 0
        }
      }

      // Perform server-side sync
      const syncedCart = await this.cartService.syncGuestCartToUser(guestSessionId)

      // Clear guest session after successful sync
      clearClientCartSession()

      console.log('✅ Cart Sync: Successfully synced guest cart', {
        itemsToTransfer,
        finalCartItems: syncedCart?.items?.length || 0
      })

      return {
        success: true,
        cart: syncedCart,
        itemsTransferred: itemsToTransfer,
        duplicatesHandled: Math.max(0, itemsToTransfer - (syncedCart?.items?.length || 0))
      }

    } catch (error) {
      console.error('❌ Cart Sync: Failed to sync guest cart', error)
      
      return {
        success: false,
        cart: null,
        error: error instanceof CartError ? error.message : 'Failed to sync cart',
        itemsTransferred: 0,
        duplicatesHandled: 0
      }
    }
  }

  /**
   * Check if there's a guest cart that needs syncing
   */
  async hasGuestCartToSync(): Promise<boolean> {
    try {
      const guestSessionId = getClientCartSessionId()
      
      if (!guestSessionId || guestSessionId === 'server-session') {
        return false
      }

      const guestCart = await this.getGuestCartInfo(guestSessionId)
      return (guestCart?.items?.length || 0) > 0
    } catch (error) {
      console.error('Cart Sync: Error checking guest cart', error)
      return false
    }
  }

  /**
   * Get guest cart information for sync preview
   */
  async getGuestCartInfo(sessionId?: string): Promise<Cart | null> {
    try {
      const guestSessionId = sessionId || getClientCartSessionId()
      
      if (!guestSessionId || guestSessionId === 'server-session') {
        return null
      }

      // Temporarily get guest cart (this won't work with current API structure)
      // For now, we'll rely on server-side sync logic
      return null
    } catch (error) {
      console.error('Cart Sync: Error getting guest cart info', error)
      return null
    }
  }

  /**
   * Handle sync conflicts and provide user choice
   */
  async handleSyncConflicts(): Promise<SyncResult> {
    // For now, we only support merge strategy
    // This could be extended to support different merge strategies
    return await this.syncGuestCartToUser()
  }

  /**
   * Cleanup orphaned guest sessions
   */
  static cleanupGuestSession(): void {
    try {
      clearClientCartSession()
      console.log('🧹 Cart Sync: Cleaned up guest session')
    } catch (error) {
      console.error('Cart Sync: Error cleaning up guest session', error)
    }
  }
}

// Singleton instance for global use
export const cartSyncManager = new CartSyncManager()

/**
 * Hook-like function for React components to handle cart sync
 */
export async function useCartSync(): Promise<{
  syncGuestCart: () => Promise<SyncResult>
  hasGuestCart: () => Promise<boolean>
  cleanupGuestSession: () => void
}> {
  return {
    syncGuestCart: () => cartSyncManager.syncGuestCartToUser(),
    hasGuestCart: () => cartSyncManager.hasGuestCartToSync(),
    cleanupGuestSession: CartSyncManager.cleanupGuestSession
  }
}

/**
 * Utility function to automatically sync cart after authentication
 * Should be called in auth callback or login success handler
 */
export async function autoSyncCartAfterAuth(): Promise<SyncResult> {
  console.log('🔄 Cart Sync: Auto-syncing cart after authentication')
  return await cartSyncManager.syncGuestCartToUser()
}

/**
 * Utility function to check if sync is needed before showing UI
 */
export async function shouldShowSyncPrompt(): Promise<boolean> {
  return await cartSyncManager.hasGuestCartToSync()
}

/**
 * Sync cart and show appropriate notifications
 */
export async function syncCartWithNotification(): Promise<SyncResult> {
  const result = await cartSyncManager.syncGuestCartToUser()
  
  if (result.success && result.itemsTransferred && result.itemsTransferred > 0) {
    // You can integrate with your toast/notification system here
    console.log(`✅ Successfully transferred ${result.itemsTransferred} items to your cart`)
    
    if (result.duplicatesHandled && result.duplicatesHandled > 0) {
      console.log(`ℹ️ ${result.duplicatesHandled} duplicate items were merged`)
    }
  }
  
  return result
}

/**
 * Error recovery for failed sync attempts
 */
export async function recoverFromSyncError(): Promise<void> {
  try {
    // Clear potentially corrupted session
    clearClientCartSession()
    
    // Refresh cart state
    const cartService = new CartService()
    await cartService.getCart()
    
    console.log('🔄 Cart Sync: Recovered from sync error')
  } catch (error) {
    console.error('❌ Cart Sync: Failed to recover from sync error', error)
  }
}
