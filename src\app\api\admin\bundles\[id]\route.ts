import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Get bundle with items
    const { data: bundle, error } = await supabase
      .from('bundles')
      .select(`
        *,
        bundle_items (
          id,
          quantity,
          products (
            id,
            title,
            price,
            discount_price,
            images
          )
        )
      `)
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching bundle:', error)
      return NextResponse.json(
        { error: 'Bundle nicht gefunden' },
        { status: 404 }
      )
    }

    return NextResponse.json(bundle)
  } catch (error) {
    console.error('Error in bundle get:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const updates = await request.json()
    const { items, ...rawBundle } = updates
    const allowedFields = [
      'title',
      'description',
      'image',
      'total_price',
      'discount_price',
      'is_available',
      'slug',
    ]
    const bundleUpdates: Record<string, unknown> = {}
    for (const [key, value] of Object.entries(rawBundle)) {
      if (allowedFields.includes(key)) bundleUpdates[key] = value
    }
    
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Update slug if title changed
    if (bundleUpdates.title) {
      bundleUpdates.slug = (bundleUpdates.title as string)
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
    }

    // Update bundle
    const { data: bundle, error: bundleError } = await supabase
      .from('bundles')
      .update({
        ...bundleUpdates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (bundleError) {
      console.error('Error updating bundle:', bundleError)
      return NextResponse.json(
        { error: 'Fehler beim Aktualisieren des Bundles' },
        { status: 500 }
      )
    }

    // Update bundle items if provided
    if (items) {
      // Delete existing items
      await supabase
        .from('bundle_items')
        .delete()
        .eq('bundle_id', id)

      // Insert new items
      const bundleItems = items.map((item: { product_id: string; quantity: number }) => ({
        bundle_id: id,
        product_id: item.product_id,
        quantity: item.quantity
      }))

      const { error: itemsError } = await supabase
        .from('bundle_items')
        .insert(bundleItems)

      if (itemsError) {
        console.error('Error updating bundle items:', itemsError)
        return NextResponse.json(
          { error: 'Fehler beim Aktualisieren der Bundle-Artikel' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({ success: true, bundle })
  } catch (error) {
    console.error('Error in bundle update:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Check if bundle is used in any orders (if you have bundle orders)
    // For now, we'll just delete it

    // Delete bundle (cascade will delete bundle_items)
    const { error } = await supabase
      .from('bundles')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting bundle:', error)
      return NextResponse.json(
        { error: 'Fehler beim Löschen des Bundles' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in bundle delete:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}
