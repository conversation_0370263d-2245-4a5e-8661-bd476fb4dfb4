@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --card: 0 0% 100%;
  --card-foreground: 0 0% 14.5%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 14.5%;
  --primary: 20 91% 48%;
  --primary-foreground: 0 0% 98.5%;
  --secondary: 0 0% 97%;
  --secondary-foreground: 20 91% 48%;
  --muted: 0 0% 97%;
  --muted-foreground: 0 0% 45%;
  --accent: 0 0% 97%;
  --accent-foreground: 20 91% 48%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --border: 0 0% 92.2%;
  --input: 0 0% 92.2%;
  --ring: 20 91% 48%;
  --chart-1: 20 91% 48%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --radius: 0.625rem;

  /* Enhanced Visual Variables */
  --gradient-primary: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
  --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--accent)) 100%);
  --gradient-hero: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--background)) 50%, hsl(var(--primary) / 0.05) 100%);
  --gradient-card: linear-gradient(145deg, hsl(var(--card)) 0%, hsl(var(--primary) / 0.02) 100%);
  --shadow-soft: 0 2px 8px -2px hsl(var(--foreground) / 0.1);
  --shadow-medium: 0 4px 16px -4px hsl(var(--foreground) / 0.15);
  --shadow-strong: 0 8px 32px -8px hsl(var(--foreground) / 0.2);
  --shadow-glow: 0 0 20px hsl(var(--primary) / 0.3);
  --shadow-glow-hover: 0 0 30px hsl(var(--primary) / 0.4);
  --background: 0 0% 100%;
  --foreground: 0 0% 14.5%;
  --sidebar: 0 0% 98.5%;
  --sidebar-foreground: 0 0% 14.5%;
  --sidebar-primary: 20 91% 48%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 97%;
  --sidebar-accent-foreground: 20 91% 48%;
  --sidebar-border: 0 0% 92.2%;
  --sidebar-ring: 20 91% 48%;
}



body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-sans);
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: var(--shadow-glow);
  }
  50% {
    box-shadow: var(--shadow-glow-hover);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Enhanced Utility Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, hsl(var(--muted) / 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Enhanced Shadows */
.shadow-soft {
  box-shadow: var(--shadow-soft);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-strong {
  box-shadow: var(--shadow-strong);
}

.shadow-glow {
  box-shadow: var(--shadow-glow);
}

.shadow-glow-hover {
  box-shadow: var(--shadow-glow-hover);
}

/* Enhanced Gradients */
.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-hero {
  background: var(--gradient-hero);
}

.bg-gradient-card {
  background: var(--gradient-card);
}

/* Enhanced Transitions */
.transition-all-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform-smooth {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-shadow-smooth {
  transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-strong);
}

.hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow-hover);
}

.hover-scale {
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Liquid Glass Cookie Banner Styles */
.liquid-glass-container {
  position: relative;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  padding: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.liquid-glass-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(234, 88, 12, 0.1) 0%,
    rgba(249, 115, 22, 0.1) 50%,
    rgba(251, 146, 60, 0.1) 100%
  );
  border-radius: 24px;
  z-index: -1;
}

.liquid-glass-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-glass-icon-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-glass-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
}

.liquid-glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.liquid-glass-button-primary {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.3) 0%,
    hsl(var(--primary) / 0.3) 100%
  );
  color: white;
  box-shadow:
    0 4px 16px hsl(var(--primary) / 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-glass-button-primary::before {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.4) 0%,
    hsl(var(--primary) / 0.4) 100%
  );
  opacity: 0;
}

.liquid-glass-button-primary:hover::before {
  opacity: 1;
}

.liquid-glass-button-primary:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px hsl(var(--primary) / 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-glass-button-secondary {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  color: white;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.liquid-glass-button-secondary::before {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  opacity: 0;
}

.liquid-glass-button-secondary:hover::before {
  opacity: 1;
}

.liquid-glass-button-secondary:hover {
  transform: translateY(-1px);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-glass-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(255, 255, 255, 0.04) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Mobile Responsive Improvements */
@media (max-width: 768px) {
  .liquid-glass-container {
    padding: 20px;
    border-radius: 20px;
    margin: 0 8px;
  }

  .liquid-glass-button {
    padding: 14px 18px;
    font-size: 15px;
    border-radius: 14px;
  }

  .liquid-glass-card {
    padding: 14px;
    border-radius: 14px;
  }

  .liquid-glass-icon {
    width: 44px;
    height: 44px;
    border-radius: 14px;
  }

  .liquid-glass-icon-small {
    width: 32px;
    height: 32px;
    border-radius: 10px;
  }
}

/* Enhanced Focus States for Accessibility */
.liquid-glass-button:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

button:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* Smooth Backdrop Animation */
@keyframes backdropFadeIn {
  from {
    backdrop-filter: blur(0px);
    background-color: rgba(0, 0, 0, 0);
  }
  to {
    backdrop-filter: blur(8px);
    background-color: rgba(0, 0, 0, 0.2);
  }
}

@keyframes slideUpFadeIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Performance Optimizations */
.liquid-glass-container,
.liquid-glass-button,
.liquid-glass-card,
.liquid-glass-icon,
.liquid-glass-icon-small {
  will-change: transform;
  transform: translateZ(0);
}

/* Avoid mobile browser zoom on form inputs */
@media (max-width: 768px) {
  input,
  textarea,
  select {
    font-size: 16px;
  }
}

/* Cart V2 Mobile Optimizations */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

@media (max-width: 768px) {
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }
}

.mobile-drawer-optimized {
  /* Enhanced mobile drawer behavior */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 640px) {
  .mobile-drawer-optimized {
    width: 100vw !important;
    max-width: 100vw !important;
    height: 100vh;
    border-radius: 0;
  }
}

.mobile-quantity-controls {
  /* Better spacing for mobile touch */
  gap: 0;
}

@media (max-width: 768px) {
  .mobile-quantity-controls {
    border-width: 2px;
    border-radius: 12px;
  }

  .mobile-quantity-controls button {
    border-radius: 10px;
  }
}

.mobile-add-to-cart {
  /* Enhanced mobile add to cart button */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 768px) {
  .mobile-add-to-cart {
    min-height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    padding: 12px 20px;
  }

  .mobile-add-to-cart:active {
    transform: scale(0.96);
  }
}

/* Mobile Cart Animations */
@media (max-width: 768px) {
  .cart-item-mobile {
    padding: 16px;
    border-radius: 16px;
    margin-bottom: 12px;
  }

  .cart-drawer-content {
    padding: 20px 16px;
  }

  .cart-summary-mobile {
    padding: 20px 16px;
    border-radius: 20px 20px 0 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
}

/* Enhanced Mobile Scrolling */
@media (max-width: 768px) {
  .cart-scroll-area {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  .cart-scroll-area::-webkit-scrollbar {
    display: none;
  }
}

/* Mobile-specific hover states (remove on touch devices) */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-\[1\.02\]:hover {
    transform: none;
  }

  .hover\:shadow-md:hover {
    box-shadow: none;
  }
}

/* Mobile Cart Item Enhancements */
@media (max-width: 768px) {
  .mobile-cart-item {
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .mobile-cart-item:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-out;
  }
}

/* Safe Area Support for Mobile */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .pb-safe {
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
  }
}

/* Mobile Drawer Improvements */
@media (max-width: 640px) {
  .cart-drawer-content {
    padding: 16px;
  }

  .cart-summary-mobile {
    margin: 0 -16px;
    padding: 20px 16px;
    border-radius: 20px 20px 0 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(12px);
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  }
}

/* Improved Touch Feedback */
@media (max-width: 768px) {
  .touch-target:active {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(0.95);
  }

  .mobile-add-to-cart:active {
    background-color: rgba(0, 0, 0, 0.9);
    transform: scale(0.96);
  }

  button:active {
    transition: all 0.1s ease-out;
  }
}

/* Mobile Cart Summary Enhancements */
@media (max-width: 768px) {
  .mobile-cart-summary {
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .mobile-cart-summary .progress {
    height: 6px;
    border-radius: 3px;
  }

  .mobile-cart-summary .badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 8px;
  }
}

/* Enhanced Mobile Animations */
@media (max-width: 768px) {
  .animate-in {
    animation-duration: 0.4s;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .slide-in-from-right-2 {
    animation-name: slideInFromRight;
  }

  .slide-in-from-bottom-2 {
    animation-name: slideInFromBottom;
  }

  @keyframes slideInFromRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideInFromBottom {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* Mobile Empty Cart Enhancements */
@media (max-width: 768px) {
  .mobile-empty-cart {
    padding: 32px 16px;
  }

  .mobile-empty-cart-icon {
    margin-bottom: 24px;
  }

  .mobile-empty-cart h3 {
    font-size: 1.5rem;
    margin-bottom: 12px;
  }

  .mobile-empty-cart p {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 32px;
  }

  .mobile-empty-cart .mobile-add-to-cart {
    min-height: 52px;
    font-size: 16px;
    font-weight: 600;
  }
}

.dark {
  --background: 0 0% 14.5%;
  --foreground: 0 0% 98.5%;
  --card: 0 0% 20.5%;
  --card-foreground: 0 0% 98.5%;
  --popover: 0 0% 20.5%;
  --popover-foreground: 0 0% 98.5%;
  --primary: 20 91% 48%;
  --primary-foreground: 0 0% 98.5%;
  --secondary: 0 0% 26.9%;
  --secondary-foreground: 0 0% 98.5%;
  --muted: 0 0% 26.9%;
  --muted-foreground: 0 0% 75%;
  --accent: 0 0% 26.9%;
  --accent-foreground: 0 0% 98.5%;
  --destructive: 0 62% 30%;
  --border: 0 0% 100% / 10%;
  --input: 0 0% 100% / 15%;
  --ring: 20 91% 48%;
  --chart-1: 20 91% 48%;
  --chart-2: 173 58% 39%;
  --chart-3: 43 74% 66%;
  --chart-4: 27 87% 67%;
  --chart-5: 197 37% 24%;
  --sidebar: 0 0% 20.5%;
  --sidebar-foreground: 0 0% 98.5%;
  --sidebar-primary: 20 91% 48%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 26.9%;
  --sidebar-accent-foreground: 0 0% 98.5%;
  --sidebar-border: 0 0% 100% / 10%;
  --sidebar-ring: 20 91% 48%;
}

@layer base {
  * {
    border-color: hsl(var(--border));
    outline-color: hsl(var(--ring) / 0.5);
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}
