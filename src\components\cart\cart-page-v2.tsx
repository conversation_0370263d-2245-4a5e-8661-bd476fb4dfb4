'use client'

import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ArrowLeft, ShoppingCart, Truck, Package } from 'lucide-react'
import Link from 'next/link'

import { useCartQuery, useCartSummary } from '@/lib/cart-v2/queries'
import { CartItemV2, CartLoadingV2, CartErrorV2, CartErrorBoundary } from '@/lib/cart-v2/components'
import { formatCurrency } from '@/lib/utils'

interface CartPageV2Props {
  locale: string
}

export function CartPageV2({ locale }: CartPageV2Props) {
  const t = useTranslations('cart')
  const tc = useTranslations('common')
  
  const { data: cart, isLoading, error } = useCartQuery()
  const { data: summary } = useCartSummary()

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href={`/${locale}/shop`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {tc('continueShopping')}
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
        </div>
        <CartLoadingV2 />
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href={`/${locale}/shop`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {tc('continueShopping')}
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
        </div>
        <CartErrorV2 error={error} />
      </div>
    )
  }

  if (!cart || !cart.items || cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href={`/${locale}/shop`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {tc('continueShopping')}
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
        </div>
        
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <ShoppingCart className="mx-auto h-24 w-24 text-muted-foreground mb-4" />
            <h2 className="text-2xl font-bold mb-4">{t('empty')}</h2>
            <p className="text-muted-foreground mb-8">
              {t('emptyDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href={`/${locale}/coffee-box-builder`}>
                  <Package className="mr-2 h-4 w-4" />
                  {t('coffeeBoxBuilder')}
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href={`/${locale}/shop`}>
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  {tc('continueShopping')}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const itemCount = cart.items.reduce((sum, item) => sum + item.quantity, 0)

  return (
    <CartErrorBoundary>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Button variant="ghost" asChild className="mb-4">
            <Link href={`/${locale}/shop`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              {tc('continueShopping')}
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">
            {t('title')} ({itemCount})
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {cart.items.map((item, index) => (
              <div
                key={item.id}
                className="animate-in slide-in-from-left-2 duration-300"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <CartItemV2 item={item} />
              </div>
            ))}
          </div>

          {/* Cart Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-4">{t('summary')}</h2>
                
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between">
                    <span>{t('subtotal')}</span>
                    <span>{formatCurrency(summary?.subtotal || 0)}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="flex items-center gap-2">
                      <Truck className="h-4 w-4" />
                      {t('shipping')}
                    </span>
                    <span>
                      {summary?.shipping_cost === 0 ? (
                        <span className="text-green-600 font-medium">{tc('free')}</span>
                      ) : (
                        formatCurrency(summary?.shipping_cost || 0)
                      )}
                    </span>
                  </div>
                  
                  {summary && summary.shipping_cost > 0 && (
                    <div className="text-sm text-muted-foreground">
                      {t('freeShippingAt', { amount: formatCurrency(90) })}
                    </div>
                  )}
                  
                  <div className="border-t pt-3">
                    <div className="flex justify-between text-lg font-semibold">
                      <span>{t('total')}</span>
                      <span>{formatCurrency(summary?.total || 0)}</span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {t('vatIncluded')}
                    </div>
                  </div>
                </div>

                <Button asChild size="lg" className="w-full">
                  <Link href={`/${locale}/checkout`}>
                    {t('proceedToCheckout')}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </CartErrorBoundary>
  )
}
