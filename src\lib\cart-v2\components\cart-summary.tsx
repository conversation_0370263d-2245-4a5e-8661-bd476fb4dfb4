'use client'

import { useTranslations } from 'next-intl'
import { Truck, Gift, Info, CheckCircle } from 'lucide-react'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

import type { CartSummary } from '../types'
import { formatCurrency } from '@/lib/utils'

interface CartSummaryV2Props {
  summary: CartSummary | null
  compact?: boolean
}

export function CartSummaryV2({ summary, compact = false }: CartSummaryV2Props) {
  const t = useTranslations('cart')

  if (!summary) return null

  const freeShippingThreshold = 90
  const remainingForFreeShipping = Math.max(0, freeShippingThreshold - summary.subtotal)
  const hasDiscount = summary.discount_amount > 0
  const hasFreeShipping = summary.subtotal >= freeShippingThreshold
  const shippingProgress = Math.min(100, (summary.subtotal / freeShippingThreshold) * 100)

  return (
    <TooltipProvider>
      <Card className={`mobile-cart-summary transition-all duration-200 hover:shadow-sm ${compact ? 'p-3' : ''}`}>
        <CardContent className={`space-y-3 ${compact ? 'p-3' : 'p-4 sm:p-4'}`}>
          {/* Subtotal */}
          <div className="flex justify-between items-center">
            <span className={compact ? 'text-sm' : ''}>{t('subtotal')}</span>
            <span className={`font-medium ${compact ? 'text-sm' : ''}`}>
              {formatCurrency(summary.subtotal)}
            </span>
          </div>

          {/* Discount */}
          {hasDiscount && (
            <div className="flex justify-between items-center text-green-600 animate-in fade-in-0 duration-300">
              <span className={`flex items-center gap-1 ${compact ? 'text-sm' : ''}`}>
                <Gift className="h-4 w-4" aria-hidden="true" />
                {t('discount')}
              </span>
              <span className={`font-medium ${compact ? 'text-sm' : ''}`}>
                -{formatCurrency(summary.discount_amount)}
              </span>
            </div>
          )}

        {/* Shipping */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className={`flex items-center gap-1 ${compact ? 'text-sm' : ''}`}>
              <Truck className="h-4 w-4" aria-hidden="true" />
              {t('shipping')}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs max-w-xs">
                    {t('shippingTooltip', { threshold: formatCurrency(freeShippingThreshold) })}
                  </p>
                </TooltipContent>
              </Tooltip>
            </span>
            <div className="text-right">
              {summary.shipping_cost === 0 ? (
                <Badge
                  variant="secondary"
                  className={`text-green-600 bg-green-50 border-green-200 flex items-center gap-1 ${compact ? 'text-xs' : ''}`}
                >
                  <CheckCircle className="h-3 w-3" />
                  {t('free')}
                </Badge>
              ) : (
                <span className={`font-medium ${compact ? 'text-sm' : ''}`}>
                  {formatCurrency(summary.shipping_cost)}
                </span>
              )}
            </div>
          </div>

          {/* Free Shipping Progress */}
          {!hasFreeShipping && (
            <div className="space-y-2 animate-in fade-in-0 duration-300">
              <div className={`text-muted-foreground ${compact ? 'text-xs' : 'text-sm'}`}>
                {t('freeShippingProgress', {
                  amount: formatCurrency(remainingForFreeShipping),
                  threshold: formatCurrency(freeShippingThreshold)
                })}
              </div>
              <div className="space-y-1">
                <Progress
                  value={shippingProgress}
                  className="h-2 transition-all duration-500"
                  aria-label={t('freeShippingProgress')}
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{formatCurrency(summary.subtotal)}</span>
                  <span>{formatCurrency(freeShippingThreshold)}</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Tax Info */}
        {summary.tax_amount > 0 && (
          <div className="flex justify-between items-center text-muted-foreground">
            <span className={compact ? 'text-sm' : ''}>{t('tax')}</span>
            <span className={compact ? 'text-sm' : ''}>
              {formatCurrency(summary.tax_amount)}
            </span>
          </div>
        )}

        <Separator />

        {/* Total */}
        <div className="flex justify-between items-center">
          <span className={`font-semibold ${compact ? 'text-base' : 'text-lg'}`}>
            {t('total')}
          </span>
          <span className={`font-bold ${compact ? 'text-base' : 'text-lg'}`}>
            {formatCurrency(summary.total)}
          </span>
        </div>

        {/* VAT Notice */}
        <p className={`text-muted-foreground text-center ${compact ? 'text-xs' : 'text-sm'}`}>
          {t('vatIncluded')}
        </p>
      </CardContent>
    </Card>
    </TooltipProvider>
  )
}
