import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 403 })
    }

    if (!data.threshold_amount) {
      return NextResponse.json({ error: 'Bestellwert erforderlich' }, { status: 400 })
    }

    const { data: gift, error } = await supabase
      .from('gift_thresholds')
      .insert({
        threshold_points: data.threshold_points, // Changed from threshold_amount to threshold_points
        gift_product_ids: data.gift_product_ids || [],
        is_active: data.is_active ?? true,
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating gift threshold:', error)
      return NextResponse.json({ error: 'Fehler beim Erstellen' }, { status: 500 })
    }

    return NextResponse.json({ success: true, gift })
  } catch (error) {
    console.error('Error in gift create:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}

