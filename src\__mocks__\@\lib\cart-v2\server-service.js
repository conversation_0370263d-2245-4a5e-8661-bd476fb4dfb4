// Manual mock for CartServiceV2
const mockCartService = {
  getCart: jest.fn(),
  addToCart: jest.fn(),
  updateCartItem: jest.fn(),
  removeFromCart: jest.fn(),
  clearCart: jest.fn(),
  syncGuestCartToUser: jest.fn(),
  calculateSummary: jest.fn(),
  validateProduct: jest.fn()
}

const CartServiceV2 = jest.fn().mockImplementation(() => mockCartService)

module.exports = {
  CartServiceV2,
  mockCartService
}
