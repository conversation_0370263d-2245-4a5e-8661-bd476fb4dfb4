'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'
import { Loader2, Percent, Calendar, Users, Info } from 'lucide-react'

interface CouponFormProps {
  locale: string
  coupon?: {
    id: string
    code: string
    type: 'percentage' | 'fixed_amount'
    value: number
    minimum_order_amount?: number
    usage_limit?: number
    valid_until: string
    is_active: boolean
  }
}

export default function CouponForm({ locale, coupon }: CouponFormProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    code: coupon?.code || '',
    type: coupon?.type || 'percentage',
    value: coupon?.value || 0,
    minimumOrderAmount: coupon?.minimum_order_amount || 0,
    usageLimit: coupon?.usage_limit || 0,
    validUntil: coupon?.valid_until ? new Date(coupon.valid_until).toISOString().split('T')[0] : '',
    isActive: coupon?.is_active ?? true,
  })

  const t = useTranslations('admin.couponsPage.create')
  const { toast } = useToast()
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validation
      if (!formData.code || !formData.type || !formData.value || !formData.validUntil) {
        toast({
          title: t('messages.error'),
          description: 'Alle Pflichtfelder müssen ausgefüllt werden',
          variant: 'destructive',
        })
        return
      }

      if (formData.type === 'percentage' && (formData.value < 0 || formData.value > 100)) {
        toast({
          title: t('messages.error'),
          description: 'Prozentualer Rabatt muss zwischen 0 und 100 liegen',
          variant: 'destructive',
        })
        return
      }

      if (formData.type === 'fixed_amount' && formData.value < 0) {
        toast({
          title: t('messages.error'),
          description: 'Fester Rabatt muss positiv sein',
          variant: 'destructive',
        })
        return
      }

      const endpoint = coupon ? `/api/admin/coupons/update` : '/api/admin/coupons/create'
      const method = 'POST'
      
      const payload = {
        ...(coupon && { id: coupon.id }),
        code: formData.code.toUpperCase(),
        type: formData.type,
        value: formData.value,
        minimumOrderAmount: formData.minimumOrderAmount || null,
        usageLimit: formData.usageLimit || null,
        validUntil: formData.validUntil,
        isActive: formData.isActive,
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (response.ok) {
        toast({
          title: t('messages.couponCreated'),
          description: t('messages.createSuccess'),
        })
        router.push(`/${locale}/admin/coupons`)
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Unknown error')
      }
    } catch (error) {
      console.error('Error saving coupon:', error)
      toast({
        title: t('messages.error'),
        description: t('messages.generalError'),
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Percent className="h-5 w-5" />
            {t('formTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid lg:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="code">{t('codeRequired')}</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                  placeholder="WELCOME10"
                  className="mt-2"
                  required
                />
                <p className="text-sm text-muted-foreground mt-1">
                  {t('codeHelp')}
                </p>
              </div>

              <div>
                <Label htmlFor="type">{t('typeRequired')}</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: 'percentage' | 'fixed_amount') => 
                    setFormData({ ...formData, type: value })
                  }
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">{t('types.percentage')}</SelectItem>
                    <SelectItem value="fixed_amount">{t('types.fixed_amount')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="value">{t('valueRequired')}</Label>
                <div className="relative mt-2">
                  <Input
                    id="value"
                    type="number"
                    value={formData.value}
                    onChange={(e) => setFormData({ ...formData, value: parseFloat(e.target.value) || 0 })}
                    min="0"
                    max={formData.type === 'percentage' ? 100 : undefined}
                    step={formData.type === 'percentage' ? 1 : 0.01}
                    className="pr-10"
                    required
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    {formData.type === 'percentage' ? (
                      <Percent className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <span className="text-sm text-muted-foreground">CHF</span>
                    )}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {t('valueHelp')}
                </p>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="minimumOrderAmount">{t('minimumOrderAmount')}</Label>
                <div className="relative mt-2">
                  <Input
                    id="minimumOrderAmount"
                    type="number"
                    value={formData.minimumOrderAmount}
                    onChange={(e) => setFormData({ ...formData, minimumOrderAmount: parseFloat(e.target.value) || 0 })}
                    min="0"
                    step="0.01"
                    className="pr-12"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <span className="text-sm text-muted-foreground">CHF</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {t('minimumOrderHelp')}
                </p>
              </div>

              <div>
                <Label htmlFor="usageLimit">{t('usageLimit')}</Label>
                <div className="relative mt-2">
                  <Input
                    id="usageLimit"
                    type="number"
                    value={formData.usageLimit}
                    onChange={(e) => setFormData({ ...formData, usageLimit: parseInt(e.target.value) || 0 })}
                    min="0"
                    className="pr-10"
                  />
                  <Users className="absolute inset-y-0 right-0 flex items-center pr-3 h-4 w-4 text-muted-foreground" />
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {t('usageLimitHelp')}
                </p>
              </div>

              <div>
                <Label htmlFor="validUntil">{t('validUntilRequired')}</Label>
                <div className="relative mt-2">
                  <Input
                    id="validUntil"
                    type="date"
                    value={formData.validUntil}
                    onChange={(e) => setFormData({ ...formData, validUntil: e.target.value })}
                    className="pr-10"
                    required
                  />
                  <Calendar className="absolute inset-y-0 right-0 flex items-center pr-3 h-4 w-4 text-muted-foreground" />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: !!checked })}
                />
                <Label htmlFor="isActive" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  {t('isActive')}
                </Label>
                <div className="flex items-center">
                  <Info className="h-4 w-4 text-muted-foreground ml-2" />
                  <span className="text-sm text-muted-foreground ml-1">
                    {t('isActiveHelp')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push(`/${locale}/admin/coupons`)}
          disabled={loading}
        >
          {t('cancel')}
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('creating')}
            </>
          ) : (
            t('create')
          )}
        </Button>
      </div>
    </form>
  )
}
