/**
 * @jest-environment node
 */

// Cart V2 Integration Tests
// Comprehensive tests for the complete Cart V2 system

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals'
import type { SupabaseClient } from '@supabase/supabase-js'
import { CartService } from '../service'
import { CartServiceV2 } from '../server-service'
import { CartError, CartErrorHandler } from '../errors'
import { cartSyncManager } from '../sync'
import { CachedCartOperations, PerformanceMonitor } from '../performance'
import { clearClientCartSession } from '../../cart-session'
import type { Cart, CartItem, AddToCartParams } from '../types'

// Mock fetch for API tests
global.fetch = jest.fn()

// Mock Supabase client
const mockSupabaseClient = {
  auth: {
    getUser: jest.fn()
  },
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        maybeSingle: jest.fn()
      }))
    }))
  })),
  rpc: jest.fn()
}

describe('Cart V2 Integration Tests', () => {
  let cartService: CartService
  let serverCartService: CartServiceV2

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()
    ;(global.fetch as jest.Mock).mockClear()
    
    // Clear caches
    CachedCartOperations.invalidateAllCarts()
    PerformanceMonitor.clearMetrics()
    CartErrorHandler.clearStats()
    
    // Initialize services
    cartService = new CartService()
    serverCartService = new CartServiceV2(mockSupabaseClient as unknown as SupabaseClient)
  })

  afterEach(() => {
    clearClientCartSession()
  })

  describe('API Integration', () => {
    it('should fetch cart successfully', async () => {
      const mockCart: Cart = {
        id: 'cart-1',
        user_id: 'user-1',
        session_id: null,
        status: 'active',
        total_amount: 25.99,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ cart: mockCart, summary: null })
      })

      const result = await cartService.getCart()
      
      expect(result).toEqual(mockCart)
      expect(global.fetch).toHaveBeenCalledWith('/api/cart-v2', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include'
      })
    })

    it('should handle API errors gracefully', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: async () => ({ error: 'Database error', code: 'DATABASE_ERROR' })
      })

      await expect(cartService.getCart()).rejects.toThrow(CartError)
    })

    it('should add item to cart', async () => {
      const mockCartItem: CartItem = {
        id: 'item-1',
        cart_id: 'cart-1',
        product_id: 'product-1',
        quantity: 2,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        product: {
          id: 'product-1',
          title: 'Test Coffee',
          price: 12.99,
          images: [],
          category: 'Caffè',
          slug: 'test-coffee',
          is_available: true
        }
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, item: mockCartItem })
      })

      const params: AddToCartParams = {
        product_id: 'product-1',
        quantity: 2
      }

      const result = await cartService.addToCart(params)
      
      expect(result).toEqual(mockCartItem)
      expect(global.fetch).toHaveBeenCalledWith('/api/cart-v2', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(params)
      })
    })
  })

  describe('Server-Side Service', () => {
    it('should validate product before adding to cart', async () => {
      // Mock product validation failure
      mockSupabaseClient.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn(() => Promise.resolve({ 
              data: null, 
              error: { code: 'PGRST116' } 
            }))
          }))
        }))
      })

      await expect(
        serverCartService.addToCart({
          product_id: 'invalid-product',
          quantity: 1,
          user_id: 'user-1'
        })
      ).rejects.toThrow(CartError)
    })

    it('should use stored procedures for cart operations', async () => {
      const mockResult = [{
        cart_item_id: 'item-1',
        cart_id: 'cart-1',
        product_id: 'product-1',
        quantity: 1,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        product_title: 'Test Coffee',
        product_price: '12.99',
        product_images: [],
        product_category: 'Caffè',
        product_slug: 'test-coffee',
        product_is_available: true
      }]

      // Mock product validation
      mockSupabaseClient.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn(() => Promise.resolve({ 
              data: { 
                id: 'product-1', 
                is_available: true,
                title: 'Test Coffee'
              }, 
              error: null 
            }))
          }))
        }))
      })

      // Mock stored procedure call
      mockSupabaseClient.rpc.mockResolvedValueOnce({
        data: mockResult,
        error: null
      })

      const result = await serverCartService.addToCart({
        product_id: 'product-1',
        quantity: 1,
        user_id: 'user-1'
      })

      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith('add_to_cart_v2', {
        p_product_id: 'product-1',
        p_quantity: 1,
        p_user_id: 'user-1',
        p_session_id: null
      })

      expect(result.id).toBe('item-1')
      expect(result.quantity).toBe(1)
    })
  })

  describe('Cart Synchronization', () => {
    it('should sync guest cart to user cart', async () => {
      const mockSyncedCart: Cart = {
        id: 'cart-1',
        user_id: 'user-1',
        session_id: null,
        status: 'active',
        total_amount: 25.99,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, cart: mockSyncedCart })
      })

      const result = await cartSyncManager.syncGuestCartToUser()
      
      expect(result.success).toBe(true)
      expect(result.cart).toEqual(mockSyncedCart)
    })

    it('should handle sync errors gracefully', async () => {
      ;(global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: async () => ({ error: 'Sync failed' })
      })

      const result = await cartSyncManager.syncGuestCartToUser()
      
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
    })
  })

  describe('Performance and Caching', () => {
    it('should cache cart data', async () => {
      const mockCart: Cart = {
        id: 'cart-1',
        user_id: 'user-1',
        session_id: null,
        status: 'active',
        total_amount: 25.99,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      const fetchFn = jest.fn().mockResolvedValue(mockCart)
      
      // First call should fetch from source
      const result1 = await CachedCartOperations.getCachedCart('user-1', undefined, fetchFn)
      expect(fetchFn).toHaveBeenCalledTimes(1)
      expect(result1).toEqual(mockCart)

      // Second call should use cache
      const result2 = await CachedCartOperations.getCachedCart('user-1', undefined, fetchFn)
      expect(fetchFn).toHaveBeenCalledTimes(1) // Still 1, not called again
      expect(result2).toEqual(mockCart)
    })

    it('should track performance metrics', async () => {
      const endTimer = PerformanceMonitor.startTimer('test_operation')
      
      // Simulate some work
      await new Promise(resolve => setTimeout(resolve, 10))
      
      const duration = endTimer()
      
      expect(duration).toBeGreaterThan(0)
      
      const metrics = PerformanceMonitor.getMetrics('test_operation')
      expect(metrics).toBeDefined()
      expect(metrics!.count).toBe(1)
      expect(metrics!.avg).toBeGreaterThan(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors with retry', async () => {
      let callCount = 0
      const mockOperation = jest.fn().mockImplementation(() => {
        callCount++
        if (callCount < 3) {
          throw new CartError('NETWORK_ERROR', 'Network failed')
        }
        return Promise.resolve('success')
      })

      // This would need to be implemented with the retry wrapper
      // For now, just test error handling
      try {
        await mockOperation()
      } catch (error) {
        const recovery = await CartErrorHandler.handleError(
          error as CartError,
          { operation: 'test', retryCount: 0 }
        )
        
        expect(recovery.shouldRetry).toBe(true)
        expect(recovery.retryDelay).toBeGreaterThan(0)
      }
    })

    it('should track error statistics', async () => {
      const error = new CartError('VALIDATION_ERROR', 'Test error')
      
      await CartErrorHandler.handleError(error, { operation: 'test' })
      
      const stats = CartErrorHandler.getErrorStats()
      expect(stats.VALIDATION_ERROR).toBeDefined()
      expect(stats.VALIDATION_ERROR.count).toBe(1)
      expect(stats.VALIDATION_ERROR.severity).toBe('low')
    })
  })

  describe('End-to-End Workflows', () => {
    it('should complete full cart workflow', async () => {
      // Mock successful API responses for complete workflow
      const mockCart: Cart = {
        id: 'cart-1',
        user_id: 'user-1',
        session_id: null,
        status: 'active',
        total_amount: 0,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      const mockCartItem: CartItem = {
        id: 'item-1',
        cart_id: 'cart-1',
        product_id: 'product-1',
        quantity: 2,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        product: {
          id: 'product-1',
          title: 'Test Coffee',
          price: 12.99,
          images: [],
          category: 'Caffè',
          slug: 'test-coffee',
          is_available: true
        }
      }

      // Mock sequence of API calls
      ;(global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ cart: mockCart, summary: null })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, item: mockCartItem })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, item: { ...mockCartItem, quantity: 3 } })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true })
        })

      // 1. Get initial cart
      const initialCart = await cartService.getCart()
      expect(initialCart).toEqual(mockCart)

      // 2. Add item to cart
      const addedItem = await cartService.addToCart({
        product_id: 'product-1',
        quantity: 2
      })
      expect(addedItem).toEqual(mockCartItem)

      // 3. Update item quantity
      const updatedItem = await cartService.updateCartItem({
        item_id: 'item-1',
        quantity: 3
      })
      expect(updatedItem.quantity).toBe(3)

      // 4. Remove item from cart
      await cartService.removeFromCart({ item_id: 'item-1' })

      // Verify all API calls were made
      expect(global.fetch).toHaveBeenCalledTimes(4)
    })
  })
})
