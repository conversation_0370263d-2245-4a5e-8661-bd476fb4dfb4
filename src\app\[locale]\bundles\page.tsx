import { createClient } from '@/lib/supabase/server'
import { getTranslations } from 'next-intl/server';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'
import { Package, ShoppingCart, Star, Gift } from 'lucide-react'
import { AddToCartButton } from '@/components/cart/add-to-cart-button'

export const dynamic = 'force-dynamic';

interface BundlesPageProps {
  params: Promise<{ locale: string }>;
}

export default async function BundlesPage({ params }: BundlesPageProps) {
  const { locale } = await params;
  const supabase = await createClient()

  // Get all available bundles with their items
  const { data: bundles, error } = await supabase
    .from('bundles')
    .select(`
      *,
      bundle_items (
        quantity,
        products (
          id,
          title,
          price,
          discount_price,
          images
        )
      )
    `)
    .eq('is_available', true)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching bundles:', error)
  }

    const availableBundles = bundles || []
  const t = await getTranslations({ locale, namespace: 'bundles' });
  const tNav = await getTranslations({ locale, namespace: 'navigation' });

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <BundlesContent bundles={availableBundles} locale={locale} t={t} tNav={tNav} />
      </div>
    </div>
  )
}

interface Bundle {
  id: string;
  title: string;
  description: string;
  image?: string;
  total_price: number;
  discount_price?: number;
  slug: string;
  bundle_items?: Array<{
    quantity: number;
    products?: { title?: string };
  }>;
}

function BundlesContent({ bundles, locale, t, tNav }: { bundles: Bundle[]; locale: string; t: Awaited<ReturnType<typeof getTranslations>>; tNav: Awaited<ReturnType<typeof getTranslations>> }) {

  return (
    <>
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          {t('subtitle')}
        </p>
      </div>

      {/* Benefits */}
      <div className="grid md:grid-cols-3 gap-6 mb-12">
        <Card className="text-center">
          <CardContent className="pt-6">
            <Gift className="h-12 w-12 text-primary mx-auto mb-4" />
            <h3 className="font-semibold mb-2">{t('benefits.exclusiveDiscounts.title')}</h3>
            <p className="text-sm text-muted-foreground">
              {t('benefits.exclusiveDiscounts.description')}
            </p>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardContent className="pt-6">
            <Star className="h-12 w-12 text-primary mx-auto mb-4" />
            <h3 className="font-semibold mb-2">{t('benefits.curatedSelection.title')}</h3>
            <p className="text-sm text-muted-foreground">
              {t('benefits.curatedSelection.description')}
            </p>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardContent className="pt-6">
            <Package className="h-12 w-12 text-primary mx-auto mb-4" />
            <h3 className="font-semibold mb-2">{t('benefits.perfectSets.title')}</h3>
            <p className="text-sm text-muted-foreground">
              {t('benefits.perfectSets.description')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Bundles Grid */}
      {bundles.length === 0 ? (
        <div className="text-center py-12">
          <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">{t('noBundles')}</h3>
          <p className="text-muted-foreground mb-6">
            {t('noBundlesDescription')}
          </p>
          <Button asChild>
            <Link href={`/${locale}/shop`}>
              {t('exploreProducts')}
            </Link>
          </Button>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {bundles.map((bundle) => {
            const hasDiscount = bundle.discount_price && bundle.discount_price < bundle.total_price
            const displayPrice = hasDiscount ? (bundle.discount_price || bundle.total_price) : bundle.total_price
            const savings = hasDiscount ? bundle.total_price - (bundle.discount_price || 0) : 0
            const savingsPercentage = hasDiscount ? Math.round((savings / bundle.total_price) * 100) : 0
            
            return (
              <Card key={bundle.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                {/* Bundle Image */}
                <div className="aspect-video relative bg-muted">
                  {bundle.image ? (
                    <Image
                      src={bundle.image}
                      alt={bundle.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <Package className="h-16 w-16 text-muted-foreground" />
                    </div>
                  )}
                  {hasDiscount && (
                    <div className="absolute top-4 right-4">
                      <Badge variant="destructive" className="text-white">
                        -{savingsPercentage}%
                      </Badge>
                    </div>
                  )}
                </div>

                <CardHeader>
                  <CardTitle className="line-clamp-2">{bundle.title}</CardTitle>
                </CardHeader>

                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {bundle.description}
                  </p>

                  {/* Included Products */}
                  <div>
                    <h4 className="font-medium mb-2">{t('includedProducts')}</h4>
                    <div className="space-y-1">
                      {bundle.bundle_items?.slice(0, 3).map((item: {
                        quantity: number
                        products?: { title?: string }
                      }, index: number) => (
                        <div key={index} className="text-sm text-muted-foreground flex justify-between">
                          <span className="line-clamp-1">{item.products?.title}</span>
                          <span>{item.quantity}x</span>
                        </div>
                      ))}
                      {bundle.bundle_items && bundle.bundle_items.length > 3 && (
                        <div className="text-sm text-muted-foreground">
                          +{bundle.bundle_items.length - 3} {t('moreProducts')}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-2xl font-bold text-primary">
                        {formatCurrency(displayPrice)}
                      </span>
                      {hasDiscount && (
                        <span className="text-lg text-muted-foreground line-through">
                          {formatCurrency(bundle.total_price)}
                        </span>
                      )}
                    </div>
                    {hasDiscount && (
                      <div className="text-sm text-green-600 font-medium">
                        {t('savings')} {formatCurrency(savings)}!
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button asChild className="flex-1">
                      <Link href={`/${locale}/bundles/${bundle.slug}`}>
                        {t('viewDetails')}
                      </Link>
                    </Button>
                    <AddToCartButton
                      productId={bundle.id}
                      variant="outline"
                      size="sm"
                    >
                      <ShoppingCart className="h-4 w-4" />
                    </AddToCartButton>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* Call to Action */}
      <div className="text-center mt-16 p-8 bg-muted rounded-lg">
        <h2 className="text-2xl font-bold mb-4">{t('callToAction.title')}</h2>
        <p className="text-muted-foreground mb-6">
          {t('callToAction.description')}
        </p>
        <Button size="lg" asChild>
          <Link href={`/${locale}/coffee-box-builder`}>
            <Package className="mr-2 h-5 w-5" />
            {tNav('coffeeBoxBuilder')}
          </Link>
        </Button>
      </div>
    </>
  )
}
