// Cart V2 Performance Optimizations
// Caching strategies, query optimizations, and performance monitoring

import type { <PERSON><PERSON>, CartSummary } from './types'

// Cache configuration
const CACHE_CONFIG = {
  CART_TTL: 5 * 60 * 1000, // 5 minutes
  SUMMARY_TTL: 2 * 60 * 1000, // 2 minutes
  PRODUCT_TTL: 15 * 60 * 1000, // 15 minutes
  MAX_CACHE_SIZE: 100,
} as const

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

/**
 * In-memory cache for cart data with TTL support
 */
class CartCache {
  private cache = new Map<string, CacheEntry<unknown>>()
  private accessOrder: string[] = []

  set<T>(key: string, data: T, ttl: number): void {
    // Remove expired entries and enforce size limit
    this.cleanup()
    
    if (this.cache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {
      // Remove least recently used item
      const oldestKey = this.accessOrder.shift()
      if (oldestKey) {
        this.cache.delete(oldestKey)
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    })

    // Update access order
    this.updateAccessOrder(key)
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined
    
    if (!entry) return null
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      this.removeFromAccessOrder(key)
      return null
    }

    // Update access order
    this.updateAccessOrder(key)
    return entry.data
  }

  delete(key: string): void {
    this.cache.delete(key)
    this.removeFromAccessOrder(key)
  }

  clear(): void {
    this.cache.clear()
    this.accessOrder = []
  }

  private cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => {
      this.cache.delete(key)
      this.removeFromAccessOrder(key)
    })
  }

  private updateAccessOrder(key: string): void {
    this.removeFromAccessOrder(key)
    this.accessOrder.push(key)
  }

  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key)
    if (index > -1) {
      this.accessOrder.splice(index, 1)
    }
  }

  getStats(): {
    size: number
    hitRate: number
    memoryUsage: string
  } {
    return {
      size: this.cache.size,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      memoryUsage: `${Math.round(JSON.stringify([...this.cache.entries()]).length / 1024)}KB`
    }
  }
}

// Global cache instance
const cartCache = new CartCache()

/**
 * Cache keys generator
 */
export const CacheKeys = {
  cart: (userId?: string, sessionId?: string) => 
    `cart:${userId || 'guest'}:${sessionId || 'none'}`,
  
  cartSummary: (cartId: string) => 
    `cart_summary:${cartId}`,
  
  product: (productId: string) => 
    `product:${productId}`,
  
  userCart: (userId: string) => 
    `user_cart:${userId}`,
  
  sessionCart: (sessionId: string) => 
    `session_cart:${sessionId}`,
} as const

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>()

  static startTimer(operation: string): () => number {
    const start = performance.now()
    return () => {
      const duration = performance.now() - start
      this.recordMetric(operation, duration)
      return duration
    }
  }

  static recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, [])
    }
    
    const metrics = this.metrics.get(operation)!
    metrics.push(duration)
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift()
    }
  }

  static getMetrics(operation: string): {
    avg: number
    min: number
    max: number
    count: number
  } | null {
    const metrics = this.metrics.get(operation)
    if (!metrics || metrics.length === 0) return null

    return {
      avg: metrics.reduce((sum, val) => sum + val, 0) / metrics.length,
      min: Math.min(...metrics),
      max: Math.max(...metrics),
      count: metrics.length,
    }
  }

  static getAllMetrics(): Record<string, ReturnType<typeof PerformanceMonitor.getMetrics>> {
    const result: Record<string, ReturnType<typeof PerformanceMonitor.getMetrics>> = {}
    
    for (const [operation] of this.metrics) {
      result[operation] = this.getMetrics(operation)
    }
    
    return result
  }

  static clearMetrics(): void {
    this.metrics.clear()
  }
}

/**
 * Cached cart operations
 */
export class CachedCartOperations {
  /**
   * Get cart with caching
   */
  static async getCachedCart(
    userId?: string, 
    sessionId?: string,
    fetchFn?: () => Promise<Cart | null>
  ): Promise<Cart | null> {
    const cacheKey = CacheKeys.cart(userId, sessionId)
    
    // Try cache first
    const cached = cartCache.get<Cart>(cacheKey)
    if (cached) {
      return cached
    }

    // Fetch from source if provided
    if (fetchFn) {
      const endTimer = PerformanceMonitor.startTimer('cart_fetch')
      try {
        const cart = await fetchFn()
        endTimer()
        
        if (cart) {
          cartCache.set(cacheKey, cart, CACHE_CONFIG.CART_TTL)
        }
        
        return cart
      } catch (error) {
        endTimer()
        throw error
      }
    }

    return null
  }

  /**
   * Get cached cart summary
   */
  static getCachedSummary(cartId: string): CartSummary | null {
    const cacheKey = CacheKeys.cartSummary(cartId)
    return cartCache.get<CartSummary>(cacheKey)
  }

  /**
   * Cache cart summary
   */
  static cacheSummary(cartId: string, summary: CartSummary): void {
    const cacheKey = CacheKeys.cartSummary(cartId)
    cartCache.set(cacheKey, summary, CACHE_CONFIG.SUMMARY_TTL)
  }

  /**
   * Invalidate cart cache
   */
  static invalidateCart(userId?: string, sessionId?: string): void {
    const cacheKey = CacheKeys.cart(userId, sessionId)
    cartCache.delete(cacheKey)
    
    // Also invalidate related caches
    if (userId) {
      cartCache.delete(CacheKeys.userCart(userId))
    }
    if (sessionId) {
      cartCache.delete(CacheKeys.sessionCart(sessionId))
    }
  }

  /**
   * Invalidate all cart-related caches
   */
  static invalidateAllCarts(): void {
    cartCache.clear()
  }

  /**
   * Get cache statistics
   */
  static getCacheStats() {
    return cartCache.getStats()
  }
}

/**
 * Debounced operations for frequent updates
 */
export class DebouncedOperations {
  private static timers = new Map<string, NodeJS.Timeout>()

  static debounce<T extends unknown[]>(
    key: string,
    fn: (...args: T) => Promise<void> | void,
    delay: number
  ): (...args: T) => void {
    return (...args: T) => {
      // Clear existing timer
      const existingTimer = this.timers.get(key)
      if (existingTimer) {
        clearTimeout(existingTimer)
      }

      // Set new timer
      const timer = setTimeout(() => {
        fn(...args)
        this.timers.delete(key)
      }, delay)

      this.timers.set(key, timer)
    }
  }

  static clearDebounce(key: string): void {
    const timer = this.timers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(key)
    }
  }

  static clearAllDebounces(): void {
    for (const timer of this.timers.values()) {
      clearTimeout(timer)
    }
    this.timers.clear()
  }
}

/**
 * Batch operations for multiple cart updates
 */
export class BatchOperations {
  private static batches = new Map<string, {
    operations: Array<() => Promise<unknown>>
    timer: NodeJS.Timeout
  }>()

  static addToBatch(
    batchKey: string,
    operation: () => Promise<unknown>,
    flushDelay: number = 100
  ): void {
    let batch = this.batches.get(batchKey)
    
    if (!batch) {
      batch = {
        operations: [],
        timer: setTimeout(() => this.flushBatch(batchKey), flushDelay)
      }
      this.batches.set(batchKey, batch)
    }

    batch.operations.push(operation)
  }

  private static async flushBatch(batchKey: string): Promise<void> {
    const batch = this.batches.get(batchKey)
    if (!batch) return

    this.batches.delete(batchKey)

    const endTimer = PerformanceMonitor.startTimer(`batch_${batchKey}`)
    
    try {
      // Execute all operations in parallel
      await Promise.allSettled(batch.operations.map(op => op()))
    } finally {
      endTimer()
    }
  }

  static async flushAllBatches(): Promise<void> {
    const batchKeys = Array.from(this.batches.keys())
    await Promise.all(batchKeys.map(key => this.flushBatch(key)))
  }
}

// Export cache instance for direct access if needed
export { cartCache }
