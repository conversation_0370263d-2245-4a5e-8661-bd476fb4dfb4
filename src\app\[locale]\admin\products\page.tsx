'use client'

import { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatCurrency } from '@/lib/utils'
import { Plus, Package, Search, Filter, Settings } from 'lucide-react'
import Link from 'next/link'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import ProductActions from '@/components/admin/product-actions'
import { AdminBackButton } from '@/components/admin/admin-back-button'
import ProductBulkActions from '@/components/admin/product-bulk-actions'

interface Product {
  id: string;
  title: string;
  description: string;
  category: string;
  coffee_type?: string;
  brand?: string;
  blend?: string;
  machine_compatibility?: string[];
  price: number;
  discount_price?: number;
  inventory_count: number;
  is_available: boolean;
  created_at: string;
  images: string[];
}

export default function AdminProductsPage() {
  const locale = useLocale()
  const router = useRouter()
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [products, setProducts] = useState<Product[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterBrand, setFilterBrand] = useState<string>('all')
  const [filterBlend, setFilterBlend] = useState<string>('all')
  const [filterCompatibility, setFilterCompatibility] = useState<string>('all')
  const [showBulkActions, setShowBulkActions] = useState(false)

  const refreshProducts = async () => {
    try {
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select('*')
        .order('created_at', { ascending: false })

      if (productsError) {
        console.error('Error fetching products:', productsError)
      } else {
        setProducts(productsData || [])
      }
    } catch (error) {
      console.error('Error refreshing products:', error)
    }
  }

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        console.log('Checking auth for products page')

        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
          console.log('No user found, redirecting to login')
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found, checking admin status')

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading products')
        setAuthChecked(true)

        // Load products
        const { data: productsData, error: productsError } = await supabase
          .from('products')
          .select('*')
          .order('created_at', { ascending: false })

        if (productsError) {
          console.error('Error fetching products:', productsError)
        } else {
          setProducts(productsData || [])
        }

        setLoading(false)
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase])

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <AdminProductsContent
      locale={locale}
      products={products}
      searchQuery={searchQuery}
      setSearchQuery={setSearchQuery}
      filterCategory={filterCategory}
      setFilterCategory={setFilterCategory}
      filterType={filterType}
      setFilterType={setFilterType}
      filterBrand={filterBrand}
      setFilterBrand={setFilterBrand}
      filterBlend={filterBlend}
      setFilterBlend={setFilterBlend}
      filterCompatibility={filterCompatibility}
      setFilterCompatibility={setFilterCompatibility}
      showBulkActions={showBulkActions}
      setShowBulkActions={setShowBulkActions}
      refreshProducts={refreshProducts}
    />
  )
}

function AdminProductsContent({
  locale,
  products,
  searchQuery,
  setSearchQuery,
  filterCategory,
  setFilterCategory,
  filterType,
  setFilterType,
  filterBrand,
  setFilterBrand,
  filterBlend,
  setFilterBlend,
  filterCompatibility,
  setFilterCompatibility,
  showBulkActions,
  setShowBulkActions,
  refreshProducts
}: {
  locale: string
  products: Product[]
  searchQuery: string
  setSearchQuery: (query: string) => void
  filterCategory: string
  setFilterCategory: (category: string) => void
  filterType: string
  setFilterType: (type: string) => void
  filterBrand: string
  setFilterBrand: (brand: string) => void
  filterBlend: string
  setFilterBlend: (blend: string) => void
  filterCompatibility: string
  setFilterCompatibility: (compatibility: string) => void
  showBulkActions: boolean
  setShowBulkActions: (show: boolean) => void
  refreshProducts: () => void
}) {
  const t = useTranslations('admin.products')

  // Filter and search products
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      // Search filter
      if (searchQuery && !product.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !product.description.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !(product.brand && product.brand.toLowerCase().includes(searchQuery.toLowerCase()))) {
        return false
      }

      // Category filter
      if (filterCategory !== 'all' && product.category !== filterCategory) return false

      // Coffee type filter
      if (filterType !== 'all' && product.coffee_type !== filterType) return false

      // Brand filter
      if (filterBrand !== 'all' && product.brand !== filterBrand) return false

      // Blend filter
      if (filterBlend !== 'all' && product.blend !== filterBlend) return false

      // Compatibility filter
      if (filterCompatibility !== 'all' && product.machine_compatibility &&
          !product.machine_compatibility.some(compat =>
            compat.toLowerCase().includes(filterCompatibility.toLowerCase())
          )) return false

      return true
    })
  }, [products, searchQuery, filterCategory, filterType, filterBrand, filterBlend, filterCompatibility])

  // Get unique values for filters
  const categories = useMemo(() => {
    const cats = [...new Set(products.map(p => p.category).filter(Boolean))] as string[]
    return cats.sort()
  }, [products])

  const coffeeTypes = useMemo(() => {
    const types = [...new Set(products.map(p => p.coffee_type).filter(Boolean))] as string[]
    return types.sort()
  }, [products])

  const brands = useMemo(() => {
    const brandList = [...new Set(products.map(p => p.brand).filter(Boolean))] as string[]
    return brandList.sort()
  }, [products])

  const blends = useMemo(() => {
    const blendList = [...new Set(products.map(p => p.blend).filter(Boolean))] as string[]
    return blendList.sort()
  }, [products])

  const compatibilities = useMemo(() => {
    const compatList = [...new Set(
      products
        .filter(p => p.machine_compatibility && p.machine_compatibility.length > 0)
        .flatMap(p => p.machine_compatibility || [])
        .filter(Boolean)
    )] as string[]
    return compatList.sort()
  }, [products])

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('title')}</h1>
          <p className="text-muted-foreground">
            {t('description')}
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowBulkActions(!showBulkActions)}
          >
            <Settings className="mr-2 h-4 w-4" />
            {t('templates.bulkActions')}
          </Button>
          <Button asChild>
            <Link href={`/${locale}/admin/products/create`}>
              <Plus className="mr-2 h-4 w-4" />
              {t('createNew')}
            </Link>
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {t('filtersTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search Bar */}
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('searchPlaceholder')}
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Filter Controls */}
          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t('filters.category')}</label>
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.allCategories')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allCategories')}</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === 'coffee' ? t('categories.coffee') : t('categories.accessories')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">{t('filters.coffeeType')}</label>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.allTypes')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allTypes')}</SelectItem>
                  {coffeeTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">{t('filters.brand')}</label>
              <Select value={filterBrand} onValueChange={setFilterBrand}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.allBrands')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allBrands')}</SelectItem>
                  {brands.map(brand => (
                    <SelectItem key={brand} value={brand}>
                      {brand}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">{t('filters.blend')}</label>
              <Select value={filterBlend} onValueChange={setFilterBlend}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.allBlends')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allBlends')}</SelectItem>
                  {blends.map(blend => (
                    <SelectItem key={blend} value={blend}>
                      {blend}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">{t('filters.compatibility')}</label>
              <Select value={filterCompatibility} onValueChange={setFilterCompatibility}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.allCompatibilities')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allCompatibilities')}</SelectItem>
                  {compatibilities.map(compatibility => (
                    <SelectItem key={compatibility} value={compatibility}>
                      {compatibility}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Filter Summary */}
          <div className="mt-4 flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {t('showingResults', { count: filteredProducts.length, total: products.length })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchQuery('')
                setFilterCategory('all')
                setFilterType('all')
                setFilterBrand('all')
                setFilterBlend('all')
                setFilterCompatibility('all')
              }}
            >
              {t('clearFilters')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Products Stats */}
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">{t('stats.total')}</p>
                <p className="text-2xl font-bold">{filteredProducts.length}</p>
                <p className="text-xs text-muted-foreground">
                  {t('stats.outOf', { total: products.length })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">{t('stats.available')}</p>
                <p className="text-2xl font-bold">
                  {filteredProducts.filter(p => p.is_available).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-primary" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">{t('stats.coffee')}</p>
                <p className="text-2xl font-bold">
                  {filteredProducts.filter(p => p.category === 'coffee').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">{t('stats.accessories')}</p>
                <p className="text-2xl font-bold">
                  {filteredProducts.filter(p => p.category === 'accessories').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      {showBulkActions && (
        <div className="mb-6">
          <ProductBulkActions
            products={filteredProducts}
            onProductsUpdate={refreshProducts}
          />
        </div>
      )}

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('allProducts')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">{t('table.product')}</th>
                  <th className="text-left py-3 px-4">{t('table.category')}</th>
                  <th className="text-left py-3 px-4">{t('table.price')}</th>
                  <th className="text-left py-3 px-4">{t('table.inventory')}</th>
                  <th className="text-left py-3 px-4">{t('table.status')}</th>
                  <th className="text-left py-3 px-4">{t('table.actions')}</th>
                </tr>
              </thead>
              <tbody>
                {filteredProducts.map((product) => (
                  <tr key={product.id} className="border-b hover:bg-muted/50">
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-3">
                        {product.images?.[0] && (
                          <div
                            className="w-10 h-10 rounded bg-cover bg-center"
                            style={{ backgroundImage: `url(${product.images[0]})` }}
                          />
                        )}
                        <div>
                          <div className="font-medium">{product.title}</div>
                          {product.brand && (
                            <div className="text-sm text-muted-foreground">
                              {product.brand}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <Badge variant="outline">
                        {product.category === 'coffee' ? t('categories.coffee') : t('categories.accessories')}
                      </Badge>
                      {product.coffee_type && (
                        <Badge variant="secondary" className="ml-2">
                          {product.coffee_type}
                        </Badge>
                      )}
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        {product.discount_price ? (
                          <>
                            <span className="font-medium text-green-600">
                              {formatCurrency(product.discount_price)}
                            </span>
                            <span className="text-sm text-muted-foreground line-through ml-2">
                              {formatCurrency(product.price)}
                            </span>
                          </>
                        ) : (
                          <span className="font-medium">
                            {formatCurrency(product.price)}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`font-medium ${
                        product.inventory_count < 10
                          ? 'text-red-600'
                          : product.inventory_count < 50
                            ? 'text-primary'
                            : 'text-green-600'
                      }`}>
                        {product.inventory_count}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <Badge variant={product.is_available ? "default" : "secondary"}>
                        {product.is_available ? t('status.available') : t('status.unavailable')}
                      </Badge>
                    </td>
                    <td className="py-3 px-4">
                      <ProductActions product={product} />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredProducts.length === 0 && products.length > 0 && (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">{t('noResults.title')}</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {t('noResults.description')}
              </p>
              <div className="mt-6">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('')
                    setFilterCategory('all')
                    setFilterType('all')
                    setFilterBrand('all')
                    setFilterBlend('all')
                    setFilterCompatibility('all')
                  }}
                >
                  {t('clearFilters')}
                </Button>
              </div>
            </div>
          )}

          {products.length === 0 && (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">{t('empty.title')}</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {t('empty.description')}
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href={`/${locale}/admin/products/create`}>
                    <Plus className="mr-2 h-4 w-4" />
                    {t('createNew')}
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
