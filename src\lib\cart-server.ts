import { createClient } from '@/lib/supabase/server'
import type { Cart } from '@/lib/cart'
import { getServerCartSessionId } from '@/lib/cart-session'

// Server-side cart functions
export async function getServerCart(userId?: string): Promise<Cart | null> {
  try {
    const supabase = await createClient()

    let query
    if (userId) {
      query = supabase
        .from('carts')
        .select(`
        *,
        cart_items (
          *,
          products (
            id,
            title,
            price,
            discount_price,
            images,
            category,
            coffee_type,
            brand
          )
        )
      `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .order('updated_at', { ascending: false })
        .limit(1)
        .maybeSingle()
    } else {
      const sessionId = await getServerCartSessionId()
      query = supabase
        .from('carts')
        .select(`
        *,
        cart_items (
          *,
          products (
            id,
            title,
            price,
            discount_price,
            images,
            category,
            coffee_type,
            brand
          )
        )
      `)
        .eq('session_id', sessionId)
        .is('user_id', null)
        .eq('status', 'active')
        .order('updated_at', { ascending: false })
        .limit(1)
        .maybeSingle()
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data, error } = await query as any

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching server cart:', error)
      return null
    }

    if (!data) return null

    const cart: Cart = {
      ...data,
      items: data.cart_items?.map((item: {
        id: string
        product_id: string
        quantity: number
        products: {
          id: string
          title: string
          price: number
          discount_price?: number
          images?: string[]
          category: string
          coffee_type?: string
          brand?: string
        }
      }) => ({
        ...item,
        product: item.products ? {
          ...item.products,
          type: item.products.coffee_type
        } : undefined
      })) || []
    }

    return cart
  } catch (error) {
    console.error('Error in getServerCart:', error)
    return null
  }
}
