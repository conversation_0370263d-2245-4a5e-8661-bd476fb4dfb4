'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Copy, Check } from 'lucide-react';
import { toast } from 'sonner';
import { useTranslations } from 'next-intl';

interface TrackingNumberDisplayProps {
  trackingNumber: string;
  className?: string;
}

export default function TrackingNumberDisplay({ trackingNumber, className = '' }: TrackingNumberDisplayProps) {
  const [copied, setCopied] = useState(false);
  const t = useTranslations('account');

  const copyTrackingNumber = async () => {
    try {
      await navigator.clipboard.writeText(trackingNumber);
      setCopied(true);
      toast.success(t('orders.trackingCopied'));
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy tracking number:', err);
      toast.error('Failed to copy tracking number');
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="font-mono">{trackingNumber}</span>
      <Button
        variant="ghost"
        size="sm"
        className="h-6 w-6 p-0"
        onClick={copyTrackingNumber}
        title={t('orders.copyTracking')}
      >
        {copied ? (
          <Check className="h-3 w-3 text-green-600" />
        ) : (
          <Copy className="h-3 w-3" />
        )}
      </Button>
    </div>
  );
}
