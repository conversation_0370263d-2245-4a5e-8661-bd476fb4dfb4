'use client'

import Image from 'next/image'
import { formatCurrency } from '@/lib/utils'
import { useTranslations } from 'next-intl'
import { Badge } from '@/components/ui/badge'
import { AddToCartButtonV2 } from '@/lib/cart-v2/components'
import { ArrowLeft, Coffee } from 'lucide-react'
import Link from 'next/link'

interface Product {
  id: string
  title: string
  description: string
  category: string
  type?: string
  brand?: string
  pack_quantity?: number
  cost_per_espresso?: number
  price: number
  discount_price?: number
  images?: string[]
  is_available: boolean
}

interface ProductPageClientProps {
  product: Product
  locale: string
}

export function ProductPageClient({ product, locale }: ProductPageClientProps) {
  const t = useTranslations('shop.product')
  const tc = useTranslations('common')

  const hasDiscount = product.discount_price && product.discount_price < product.price
  const displayPrice = hasDiscount ? (product.discount_price || product.price) : product.price

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-white to-primary/10">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <Link 
          href={`/${locale}/shop`}
          className="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors mb-6"
        >
          <ArrowLeft className="h-4 w-4" />
          {tc('back')}
        </Link>

        <div className="grid md:grid-cols-2 gap-8 items-start">
          {/* Image */}
          <div className="w-full aspect-square relative rounded-lg overflow-hidden">
            {product.images?.[0] ? (
              <Image src={product.images[0]} alt={product.title} fill className="object-cover" />
            ) : (
              <div className="w-full h-full bg-gradient-card flex items-center justify-center">
                <Coffee className="h-20 w-20 text-muted-foreground" />
              </div>
            )}
            {hasDiscount && (
              <Badge className="absolute top-2 right-2 bg-gradient-to-r from-red-500 to-red-600 text-white shadow-medium">
                -{Math.round(((product.price - (product.discount_price || 0)) / product.price) * 100)}%
              </Badge>
            )}
          </div>

          {/* Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">{product.title}</h1>
              {product.brand && <p className="text-muted-foreground mb-4">{product.brand}</p>}
              <p className="whitespace-pre-line">{product.description}</p>
            </div>

            {/* Attributes */}
            {product.category === 'coffee' && (
              <div className="space-y-2 text-sm">
                {product.type && (
                  <div className="flex justify-between"><span className="text-muted-foreground">{t('type')}:</span><span className="capitalize">{product.type}</span></div>
                )}
                {product.pack_quantity && (
                  <div className="flex justify-between"><span className="text-muted-foreground">{t('pack')}:</span><span>{product.pack_quantity} {t('pieces')}</span></div>
                )}
                {product.cost_per_espresso && (
                  <div className="flex justify-between"><span className="text-muted-foreground">{t('perEspresso')}:</span><span>{formatCurrency(product.cost_per_espresso)}</span></div>
                )}
              </div>
            )}

            {/* Price */}
            <div className="flex items-center gap-3">
              <span className="text-3xl font-bold">{formatCurrency(displayPrice)}</span>
              {hasDiscount && (
                <span className="text-muted-foreground line-through">{formatCurrency(product.price)}</span>
              )}
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <AddToCartButtonV2 productId={product.id} size="lg" className="flex-1" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
