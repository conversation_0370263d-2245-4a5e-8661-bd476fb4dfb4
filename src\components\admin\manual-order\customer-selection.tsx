'use client'

import { useState, useEffect, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Search, User, UserPlus } from 'lucide-react'
import { Badge } from '@/components/ui/badge'

interface Customer {
  id: string
  first_name: string
  last_name: string
  email: string
  phone?: string
  current_level: number
  total_points: number
}

interface SelectedCustomer {
  id?: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  isGuest: boolean
}

interface CustomerSelectionSectionProps {
  selectedCustomer: SelectedCustomer | null
  onCustomerSelect: (customer: SelectedCustomer | null) => void
}

export function CustomerSelectionSection({
  selectedCustomer,
  onCustomerSelect
}: CustomerSelectionSectionProps) {
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [customerType, setCustomerType] = useState<'existing' | 'guest'>('existing')
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<Customer[]>([])
  const [searching, setSearching] = useState(false)
  const [guestForm, setGuestForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  })

  // Search for existing customers
  useEffect(() => {
    const searchCustomers = async () => {
      if (customerType !== 'existing' || searchQuery.trim().length < 2) {
        setSearchResults([])
        return
      }

      setSearching(true)
      try {
        const { data, error } = await supabase
          .from('users')
          .select('id, first_name, last_name, email, phone, current_level, total_points')
          .or(`email.ilike.%${searchQuery}%,first_name.ilike.%${searchQuery}%,last_name.ilike.%${searchQuery}%`)
          .limit(10)

        if (error) {
          console.error('Error searching customers:', error)
          setSearchResults([])
        } else {
          setSearchResults(data || [])
        }
      } catch (error) {
        console.error('Error searching customers:', error)
        setSearchResults([])
      } finally {
        setSearching(false)
      }
    }

    const debounceTimer = setTimeout(searchCustomers, 300)
    return () => clearTimeout(debounceTimer)
  }, [searchQuery, customerType, supabase])

  // Update selected customer when guest form changes
  useEffect(() => {
    if (customerType === 'guest' && guestForm.firstName && guestForm.lastName && guestForm.email) {
      onCustomerSelect({
        firstName: guestForm.firstName,
        lastName: guestForm.lastName,
        email: guestForm.email,
        phone: guestForm.phone,
        isGuest: true
      })
    } else if (customerType === 'guest') {
      onCustomerSelect(null)
    }
  }, [customerType, guestForm, onCustomerSelect])

  const handleCustomerTypeChange = (type: 'existing' | 'guest') => {
    setCustomerType(type)
    onCustomerSelect(null)
    setSearchQuery('')
    setSearchResults([])
    setGuestForm({
      firstName: '',
      lastName: '',
      email: '',
      phone: ''
    })
  }

  const handleExistingCustomerSelect = (customer: Customer) => {
    onCustomerSelect({
      id: customer.id,
      firstName: customer.first_name,
      lastName: customer.last_name,
      email: customer.email,
      phone: customer.phone,
      isGuest: false
    })
    setSearchQuery(`${customer.first_name} ${customer.last_name} (${customer.email})`)
    setSearchResults([])
  }

  const handleGuestFormChange = (field: keyof typeof guestForm, value: string) => {
    setGuestForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          {t('manualOrderPage.customerSection.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Customer Type Selection */}
        <div className="space-y-2">
          <Label>{t('manualOrderPage.customerSection.customerType')}</Label>
          <Select
            value={customerType}
            onValueChange={(value) => handleCustomerTypeChange(value as 'existing' | 'guest')}
          >
            <SelectTrigger>
              <SelectValue placeholder={t('manualOrderPage.customerSection.selectCustomerType')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="existing">{t('manualOrderPage.customerSection.existingCustomer')}</SelectItem>
              <SelectItem value="guest">{t('manualOrderPage.customerSection.guestCustomer')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Existing Customer Search */}
        {customerType === 'existing' && (
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('manualOrderPage.customerSection.searchPlaceholder')}
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Search Results */}
            {searching && (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
              </div>
            )}

            {searchResults.length > 0 && (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {searchResults.map((customer) => (
                  <div
                    key={customer.id}
                    className="p-3 border rounded-lg hover:bg-muted cursor-pointer transition-colors"
                    onClick={() => handleExistingCustomerSelect(customer)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">
                          {customer.first_name} {customer.last_name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {customer.email}
                        </div>
                        {customer.phone && (
                          <div className="text-sm text-muted-foreground">
                            {customer.phone}
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        {customer.current_level >= 3 && (
                          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                            VIP
                          </Badge>
                        )}
                        <div className="text-xs text-muted-foreground">
                          {customer.total_points} points
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {searchQuery.length >= 2 && !searching && searchResults.length === 0 && (
              <div className="text-center py-4 text-muted-foreground">
                {t('manualOrderPage.customerSection.noCustomerFound')}
              </div>
            )}
          </div>
        )}

        {/* Guest Customer Form */}
        {customerType === 'guest' && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName">{t('manualOrderPage.guestForm.firstName')}</Label>
              <Input
                id="firstName"
                value={guestForm.firstName}
                onChange={(e) => handleGuestFormChange('firstName', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="lastName">{t('manualOrderPage.guestForm.lastName')}</Label>
              <Input
                id="lastName"
                value={guestForm.lastName}
                onChange={(e) => handleGuestFormChange('lastName', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">{t('manualOrderPage.guestForm.email')}</Label>
              <Input
                id="email"
                type="email"
                value={guestForm.email}
                onChange={(e) => handleGuestFormChange('email', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="phone">{t('manualOrderPage.guestForm.phone')}</Label>
              <Input
                id="phone"
                type="tel"
                value={guestForm.phone}
                onChange={(e) => handleGuestFormChange('phone', e.target.value)}
              />
            </div>
          </div>
        )}

        {/* Selected Customer Display */}
        {selectedCustomer && (
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              {selectedCustomer.isGuest ? (
                <UserPlus className="h-4 w-4" />
              ) : (
                <User className="h-4 w-4" />
              )}
              <span className="font-medium">{t('manualOrderPage.customerSection.customerDetails')}</span>
              {selectedCustomer.isGuest && (
                <Badge variant="outline">Guest</Badge>
              )}
            </div>
            <div className="text-sm space-y-1">
              <div><strong>Name:</strong> {selectedCustomer.firstName} {selectedCustomer.lastName}</div>
              <div><strong>Email:</strong> {selectedCustomer.email}</div>
              {selectedCustomer.phone && (
                <div><strong>Phone:</strong> {selectedCustomer.phone}</div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
