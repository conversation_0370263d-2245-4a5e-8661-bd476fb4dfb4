// Cart V2 Sync API Route
// Synchronize guest cart with user cart after authentication

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { CartServiceV2 } from '@/lib/cart-v2/server-service'
import { CartError } from '@/lib/cart-v2/errors'

/**
 * POST /api/cart-v2/sync
 * Merge guest cart with user cart after authentication
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { guest_session_id } = body
    
    if (!guest_session_id) {
      return NextResponse.json(
        { error: 'Missing required field: guest_session_id' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    const cartService = new CartServiceV2(supabase)
    
    // Get authenticated user
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'User must be authenticated to sync cart' },
        { status: 401 }
      )
    }
    
    console.log('🛒 Cart V2 API: POST sync cart', { 
      userId: user.id, 
      guestSessionId: guest_session_id 
    })
    
    const mergedCart = await cartService.syncGuestCartToUser(
      user.id, 
      guest_session_id
    )
    
    return NextResponse.json({
      success: true,
      cart: mergedCart
    })
    
  } catch (error) {
    console.error('🛒 Cart V2 API: Sync error:', error)
    
    if (error instanceof CartError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.getHttpStatus() }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
