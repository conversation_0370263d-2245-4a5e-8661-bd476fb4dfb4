// Cart V2 Error Handling
// Comprehensive error handling for the new cart system

import type { CartErrorCode, CartErrorDetails } from './types'

export class CartError extends Error {
  public readonly code: CartErrorCode
  public readonly details?: Record<string, unknown>
  public readonly timestamp: Date

  constructor(
    code: CartErrorCode,
    message: string,
    details?: Record<string, unknown>
  ) {
    super(message)
    this.name = 'CartError'
    this.code = code
    this.details = details
    this.timestamp = new Date()

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CartError)
    }
  }

  toJSON(): CartErrorDetails {
    return {
      code: this.code,
      message: this.message,
      details: this.details,
    }
  }

  getHttpStatus(): number {
    return CartErrorHandler.getHttpStatus(this)
  }

  static fromSupabaseError(error: unknown): CartError {
    const errorObj = error as { code?: string; message?: string }
    const code = mapSupabaseErrorToCartError(errorObj.code || 'UNKNOWN_ERROR')
    return new CartError(code, errorObj.message || 'Database error', {
      originalError: error,
      supabaseCode: errorObj.code
    })
  }

  static fromNetworkError(error: unknown): CartError {
    const errorMessage = error instanceof Error ? error.message : 'Network request failed'
    return new CartError('NETWORK_ERROR', errorMessage, {
      originalError: error,
    })
  }

  static validation(message: string, field?: string): CartError {
    return new CartError('VALIDATION_ERROR', message, { field })
  }

  static notFound(resource: string, id?: string): CartError {
    const message = id 
      ? `${resource} with id ${id} not found`
      : `${resource} not found`
    
    return new CartError(
      resource === 'cart' ? 'CART_NOT_FOUND' : 'ITEM_NOT_FOUND',
      message,
      { resource, id }
    )
  }

  static productNotAvailable(productId: string): CartError {
    return new CartError(
      'PRODUCT_NOT_AVAILABLE',
      'Product is not available for purchase',
      { productId }
    )
  }

  static invalidQuantity(quantity: number): CartError {
    return new CartError(
      'INVALID_QUANTITY',
      `Invalid quantity: ${quantity}. Quantity must be a positive integer.`,
      { quantity }
    )
  }
}

// Map Supabase error codes to cart error codes
function mapSupabaseErrorToCartError(supabaseCode: string): CartErrorCode {
  switch (supabaseCode) {
    case 'PGRST116': // No rows returned
      return 'CART_NOT_FOUND'
    case '23503': // Foreign key violation
      return 'ITEM_NOT_FOUND'
    case '23505': // Unique violation
      return 'VALIDATION_ERROR'
    case '42P01': // Undefined table
    case '42703': // Undefined column
      return 'DATABASE_ERROR'
    default:
      return 'DATABASE_ERROR'
  }
}

// Error message translations for user-facing errors
export const ERROR_MESSAGES = {
  en: {
    CART_NOT_FOUND: 'Your cart could not be found. Please try refreshing the page.',
    ITEM_NOT_FOUND: 'The item you\'re trying to modify is no longer in your cart.',
    PRODUCT_NOT_AVAILABLE: 'This product is currently not available.',
    INVALID_QUANTITY: 'Please enter a valid quantity.',
    DATABASE_ERROR: 'A technical error occurred. Please try again.',
    NETWORK_ERROR: 'Connection error. Please check your internet connection.',
    VALIDATION_ERROR: 'Please check your input and try again.',
    UNAUTHORIZED: 'You are not authorized to perform this action.',
    SESSION_EXPIRED: 'Your session has expired. Please refresh the page.',
  },
  de: {
    CART_NOT_FOUND: 'Ihr Warenkorb konnte nicht gefunden werden. Bitte laden Sie die Seite neu.',
    ITEM_NOT_FOUND: 'Der Artikel, den Sie ändern möchten, ist nicht mehr in Ihrem Warenkorb.',
    PRODUCT_NOT_AVAILABLE: 'Dieses Produkt ist derzeit nicht verfügbar.',
    INVALID_QUANTITY: 'Bitte geben Sie eine gültige Menge ein.',
    DATABASE_ERROR: 'Ein technischer Fehler ist aufgetreten. Bitte versuchen Sie es erneut.',
    NETWORK_ERROR: 'Verbindungsfehler. Bitte überprüfen Sie Ihre Internetverbindung.',
    VALIDATION_ERROR: 'Bitte überprüfen Sie Ihre Eingabe und versuchen Sie es erneut.',
    UNAUTHORIZED: 'Sie sind nicht berechtigt, diese Aktion auszuführen.',
    SESSION_EXPIRED: 'Ihre Sitzung ist abgelaufen. Bitte laden Sie die Seite neu.',
  },
  fr: {
    CART_NOT_FOUND: 'Votre panier n\'a pas pu être trouvé. Veuillez actualiser la page.',
    ITEM_NOT_FOUND: 'L\'article que vous essayez de modifier n\'est plus dans votre panier.',
    PRODUCT_NOT_AVAILABLE: 'Ce produit n\'est actuellement pas disponible.',
    INVALID_QUANTITY: 'Veuillez saisir une quantité valide.',
    DATABASE_ERROR: 'Une erreur technique s\'est produite. Veuillez réessayer.',
    NETWORK_ERROR: 'Erreur de connexion. Veuillez vérifier votre connexion internet.',
    VALIDATION_ERROR: 'Veuillez vérifier votre saisie et réessayer.',
    UNAUTHORIZED: 'Vous n\'êtes pas autorisé à effectuer cette action.',
    SESSION_EXPIRED: 'Votre session a expiré. Veuillez actualiser la page.',
  },
  it: {
    CART_NOT_FOUND: 'Il tuo carrello non è stato trovato. Ricarica la pagina.',
    ITEM_NOT_FOUND: 'L\'articolo che stai cercando di modificare non è più nel tuo carrello.',
    PRODUCT_NOT_AVAILABLE: 'Questo prodotto non è attualmente disponibile.',
    INVALID_QUANTITY: 'Inserisci una quantità valida.',
    DATABASE_ERROR: 'Si è verificato un errore tecnico. Riprova.',
    NETWORK_ERROR: 'Errore di connessione. Controlla la tua connessione internet.',
    VALIDATION_ERROR: 'Controlla il tuo input e riprova.',
    UNAUTHORIZED: 'Non sei autorizzato a eseguire questa azione.',
    SESSION_EXPIRED: 'La tua sessione è scaduta. Ricarica la pagina.',
  },
} as const

export function getErrorMessage(
  code: CartErrorCode,
  locale: keyof typeof ERROR_MESSAGES = 'en'
): string {
  return ERROR_MESSAGES[locale][code] || ERROR_MESSAGES.en[code]
}

/**
 * Enhanced error handling with recovery strategies
 */
export class CartErrorHandler {
  private static errorCounts = new Map<CartErrorCode, number>()
  private static lastErrors = new Map<CartErrorCode, Date>()

  /**
   * Handle cart error with automatic recovery strategies
   */
  static async handleError(
    error: CartError,
    context: {
      operation: string
      userId?: string
      sessionId?: string
      retryCount?: number
    }
  ): Promise<{
    shouldRetry: boolean
    retryDelay?: number
    recoveryAction?: () => Promise<void>
  }> {
    // Track error frequency
    this.trackError(error.code)

    console.error(`🚨 Cart Error [${error.code}]:`, {
      message: error.message,
      context,
      details: error.details,
      timestamp: error.timestamp
    })

    const retryCount = context.retryCount || 0
    const maxRetries = this.getMaxRetries(error.code)

    switch (error.code) {
      case 'NETWORK_ERROR':
        return {
          shouldRetry: retryCount < maxRetries,
          retryDelay: Math.min(1000 * Math.pow(2, retryCount), 10000), // Exponential backoff
        }

      case 'DATABASE_ERROR':
        return {
          shouldRetry: retryCount < 2,
          retryDelay: 2000,
        }

      case 'SESSION_EXPIRED':
        return {
          shouldRetry: false,
          recoveryAction: async () => {
            // Clear session and redirect to refresh
            if (typeof window !== 'undefined') {
              window.location.reload()
            }
          }
        }

      case 'CART_NOT_FOUND':
        return {
          shouldRetry: false,
          recoveryAction: async () => {
            // Clear cart cache and create new cart
            const { CachedCartOperations } = await import('./performance')
            CachedCartOperations.invalidateCart(context.userId, context.sessionId)
          }
        }

      case 'PRODUCT_NOT_AVAILABLE':
        return {
          shouldRetry: false,
          recoveryAction: async () => {
            // Remove unavailable product from cart
            console.log('Product no longer available, should be removed from cart')
          }
        }

      default:
        return {
          shouldRetry: retryCount < 1,
          retryDelay: 1000,
        }
    }
  }

  /**
   * Get HTTP status code for error
   */
  static getHttpStatus(error: CartError): number {
    switch (error.code) {
      case 'VALIDATION_ERROR':
      case 'INVALID_QUANTITY':
        return 400
      case 'UNAUTHORIZED':
        return 401
      case 'CART_NOT_FOUND':
      case 'ITEM_NOT_FOUND':
        return 404
      case 'PRODUCT_NOT_AVAILABLE':
        return 409
      case 'SESSION_EXPIRED':
        return 401
      case 'NETWORK_ERROR':
        return 503
      case 'DATABASE_ERROR':
        return 500
      default:
        return 500
    }
  }

  /**
   * Check if error is recoverable
   */
  static isRecoverable(error: CartError): boolean {
    const recoverableErrors: CartErrorCode[] = [
      'NETWORK_ERROR',
      'DATABASE_ERROR',
      'SESSION_EXPIRED',
      'CART_NOT_FOUND'
    ]
    return recoverableErrors.includes(error.code)
  }

  /**
   * Get error severity level
   */
  static getSeverity(error: CartError): 'low' | 'medium' | 'high' | 'critical' {
    switch (error.code) {
      case 'VALIDATION_ERROR':
      case 'INVALID_QUANTITY':
        return 'low'
      case 'ITEM_NOT_FOUND':
      case 'PRODUCT_NOT_AVAILABLE':
        return 'medium'
      case 'CART_NOT_FOUND':
      case 'NETWORK_ERROR':
        return 'high'
      case 'DATABASE_ERROR':
      case 'UNAUTHORIZED':
      case 'SESSION_EXPIRED':
        return 'critical'
      default:
        return 'medium'
    }
  }

  private static trackError(code: CartErrorCode): void {
    const count = this.errorCounts.get(code) || 0
    this.errorCounts.set(code, count + 1)
    this.lastErrors.set(code, new Date())
  }

  private static getMaxRetries(code: CartErrorCode): number {
    switch (code) {
      case 'NETWORK_ERROR':
        return 3
      case 'DATABASE_ERROR':
        return 2
      case 'SESSION_EXPIRED':
        return 0
      default:
        return 1
    }
  }

  /**
   * Get error statistics for monitoring
   */
  static getErrorStats(): Record<CartErrorCode, {
    count: number
    lastOccurrence: Date | null
    severity: string
  }> {
    const stats: Record<string, {
      count: number
      lastOccurrence: Date | null
      severity: string
    }> = {}

    for (const [code, count] of this.errorCounts.entries()) {
      stats[code] = {
        count,
        lastOccurrence: this.lastErrors.get(code) || null,
        severity: this.getSeverity(new CartError(code, ''))
      }
    }

    return stats
  }

  /**
   * Clear error statistics
   */
  static clearStats(): void {
    this.errorCounts.clear()
    this.lastErrors.clear()
  }
}

/**
 * Retry wrapper with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  context: {
    operationName: string
    userId?: string
    sessionId?: string
    maxRetries?: number
  }
): Promise<T> {
  let lastError: CartError | null = null
  let retryCount = 0
  const maxRetries = context.maxRetries || 3

  while (retryCount <= maxRetries) {
    try {
      return await operation()
    } catch (error) {
      const cartError = error instanceof CartError
        ? error
        : CartError.fromNetworkError(error)

      lastError = cartError

      const recovery = await CartErrorHandler.handleError(cartError, {
        operation: context.operationName,
        userId: context.userId,
        sessionId: context.sessionId,
        retryCount
      })

      if (!recovery.shouldRetry || retryCount >= maxRetries) {
        // Execute recovery action if available
        if (recovery.recoveryAction) {
          try {
            await recovery.recoveryAction()
          } catch (recoveryError) {
            console.error('Recovery action failed:', recoveryError)
          }
        }
        break
      }

      // Wait before retry
      if (recovery.retryDelay) {
        await new Promise(resolve => setTimeout(resolve, recovery.retryDelay))
      }

      retryCount++
    }
  }

  throw lastError
}
