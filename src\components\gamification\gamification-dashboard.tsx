'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Trophy, 
  Star, 
  Gift, 
  TrendingUp, 
  Award, 
  Target,
  Sparkles,
  Crown,
  Zap
} from 'lucide-react';

interface UserLevel {
  id: string;
  level: number;
  name: string;
  minimum_points: number;
  discount_percentage: number;
  points_multiplier: number;
}

interface GiftThreshold {
  id: string;
  threshold_points: number;
  gift_product_ids: string[];
  is_active: boolean;
}

interface Product {
  id: string;
  title: string;
  price: number;
  image_url?: string;
  description?: string;
}

interface GamificationData {
  currentLevel: number;
  currentLevelName: string;
  totalPoints: number;
  lifetimeSpend: number;
  pointsToNext: number;
  nextLevelName: string | null;
  progressToNext: number;
  discountPercent: number;
  pointsMultiplier: number;
  allLevels: UserLevel[];
  applicableGifts: GiftThreshold[];
  nextGiftThreshold: GiftThreshold | null;
  futureGifts: GiftThreshold[];
  giftProducts: Product[];
}

interface GamificationDashboardProps {
  className?: string;
}

export default function GamificationDashboard({ className }: GamificationDashboardProps) {
  const [data, setData] = useState<GamificationData | null>(null);
  const [loading, setLoading] = useState(true);
  const t = useTranslations('gamification');

  useEffect(() => {
    const loadGamificationData = async () => {
      try {
        setLoading(true);
        const res = await fetch('/api/account/loyalty');
        if (!res.ok) throw new Error('Failed to fetch gamification data');
        const gamificationData: GamificationData = await res.json();
        setData(gamificationData);
      } catch (err) {
        console.error('Error fetching gamification data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadGamificationData();
  }, []);

  const getLevelIcon = (levelName: string) => {
    switch (levelName.toLowerCase()) {
      case 'bronze':
        return <Award className="h-5 w-5 text-amber-600" />;
      case 'silver':
        return <Star className="h-5 w-5 text-gray-500" />;
      case 'gold':
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 'platinum':
        return <Crown className="h-5 w-5 text-purple-500" />;
      default:
        return <Award className="h-5 w-5 text-gray-400" />;
    }
  };

  const getLevelColor = (levelName: string) => {
    switch (levelName.toLowerCase()) {
      case 'bronze':
        return 'from-amber-100 to-amber-200 border-amber-300';
      case 'silver':
        return 'from-gray-100 to-gray-200 border-gray-300';
      case 'gold':
        return 'from-yellow-100 to-yellow-200 border-yellow-300';
      case 'platinum':
        return 'from-purple-100 to-purple-200 border-purple-300';
      default:
        return 'from-gray-100 to-gray-200 border-gray-300';
    }
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="h-48 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-muted-foreground">Fehler beim Laden der Gamification-Daten</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2 flex items-center justify-center gap-2">
          <Sparkles className="h-6 w-6 text-primary" />
          {t('dashboard.title')}
        </h2>
        <p className="text-muted-foreground">{t('dashboard.subtitle')}</p>
      </div>

      {/* Current Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className={`bg-gradient-to-br ${getLevelColor(data.currentLevelName)} border-2`}>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              {getLevelIcon(data.currentLevelName)}
            </div>
            <p className="text-sm text-muted-foreground">{t('currentStatus.title')}</p>
            <p className="text-xl font-bold">{data.currentLevelName}</p>
            <p className="text-xs text-muted-foreground">
              {t('currentStatus.level', { level: data.currentLevel })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Zap className="h-5 w-5 text-blue-500" />
            </div>
            <p className="text-sm text-muted-foreground">{t('loyalty.totalPoints')}</p>
            <p className="text-xl font-bold">{data.totalPoints}</p>
            <p className="text-xs text-muted-foreground">
              {t('currentStatus.points', { points: data.totalPoints })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Target className="h-5 w-5 text-green-500" />
            </div>
            <p className="text-sm text-muted-foreground">{t('benefits.discount')}</p>
            <p className="text-xl font-bold">{data.discountPercent}%</p>
            <p className="text-xs text-muted-foreground">
              {t('currentStatus.discount', { percent: data.discountPercent })}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-5 w-5 text-purple-500" />
            </div>
            <p className="text-sm text-muted-foreground">{t('benefits.pointsMultiplier')}</p>
            <p className="text-xl font-bold">{data.pointsMultiplier}x</p>
            <p className="text-xs text-muted-foreground">
              {t('currentStatus.multiplier', { multiplier: data.pointsMultiplier })}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Progress to Next Level */}
      {data.nextLevelName ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              {t('progress.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="font-medium">
                  {t('nextLevel.title', { levelName: data.nextLevelName })}
                </span>
                <Badge variant="outline">
                  {t('progress.pointsNeeded', { points: data.pointsToNext })}
                </Badge>
              </div>
              <Progress value={data.progressToNext} className="h-3" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>
                  {t('progress.progressBar', { 
                    current: data.totalPoints, 
                    total: data.totalPoints + data.pointsToNext 
                  })}
                </span>
                <span>{Math.round(data.progressToNext)}%</span>
              </div>
              {data.progressToNext > 80 && (
                <div className="text-center">
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {t('progress.almostThere')}
                  </Badge>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-6 text-center">
            <Crown className="h-12 w-12 text-purple-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">{t('progress.maxLevel')}</h3>
            <p className="text-muted-foreground">
              Herzlichen Glückwunsch! Sie haben das höchste Niveau erreicht.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Current Benefits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            {t('benefits.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
              <Target className="h-5 w-5 text-green-500" />
              <div>
                <p className="font-medium">{data.discountPercent}% {t('benefits.discount')}</p>
                <p className="text-sm text-muted-foreground">
                  Auf alle Ihre Bestellungen
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
              <TrendingUp className="h-5 w-5 text-purple-500" />
              <div>
                <p className="font-medium">{data.pointsMultiplier}x {t('benefits.pointsMultiplier')}</p>
                <p className="text-sm text-muted-foreground">
                  Verdienen Sie mehr Punkte pro Einkauf
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rewards & Gifts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            {t('rewards.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Earned Rewards */}
            {data.applicableGifts.length > 0 && (
              <div>
                <h4 className="font-semibold mb-3 text-green-700 flex items-center gap-2">
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {t('rewards.unlocked')}
                  </Badge>
                  {t('rewards.earned')}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {data.applicableGifts.map((gift) => {
                    const giftProducts = data.giftProducts.filter(p =>
                      gift.gift_product_ids.includes(p.id)
                    );
                    return (
                      <div key={gift.id} className="p-3 border rounded-lg bg-green-50 border-green-200">
                        <div className="flex items-center justify-between mb-2">
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            {gift.threshold_points} {t('loyalty.totalPoints')}
                          </Badge>
                          <Gift className="h-4 w-4 text-green-600" />
                        </div>
                        {giftProducts.length > 0 ? (
                          <div className="space-y-1">
                            {giftProducts.map((product) => (
                              <p key={product.id} className="text-sm font-medium">
                                {product.title}
                              </p>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground">
                            {gift.gift_product_ids.length} Produkt(e)
                          </p>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Next Reward */}
            {data.nextGiftThreshold && (
              <div>
                <h4 className="font-semibold mb-3 text-blue-700 flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  {t('rewards.nextReward', { points: data.nextGiftThreshold.threshold_points })}
                </h4>
                <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                  <div className="flex items-center justify-between mb-3">
                    <Badge variant="outline">
                      {t('rewards.pointsRequired', { points: data.nextGiftThreshold.threshold_points })}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {data.nextGiftThreshold.threshold_points - data.totalPoints} Punkte fehlen
                    </span>
                  </div>
                  <Progress
                    value={(data.totalPoints / data.nextGiftThreshold.threshold_points) * 100}
                    className="h-2 mb-2"
                  />
                  {data.giftProducts.filter(p =>
                    data.nextGiftThreshold!.gift_product_ids.includes(p.id)
                  ).map((product) => (
                    <p key={product.id} className="text-sm font-medium">
                      {product.title}
                    </p>
                  ))}
                </div>
              </div>
            )}

            {/* Future Rewards Preview */}
            {data.futureGifts.length > 1 && (
              <div>
                <h4 className="font-semibold mb-3 text-gray-700 flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  {t('rewards.upcoming')}
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  {data.futureGifts.slice(1, 4).map((gift) => (
                    <div key={gift.id} className="p-3 border rounded-lg bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline">
                          {gift.threshold_points} Punkte
                        </Badge>
                        <Gift className="h-4 w-4 text-gray-400" />
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {gift.gift_product_ids.length} Geschenk(e)
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* No rewards message */}
            {data.applicableGifts.length === 0 && !data.nextGiftThreshold && (
              <div className="text-center py-6">
                <Gift className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                <p className="text-muted-foreground">{t('rewards.noRewards')}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Tips Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            {t('tips.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
              <p className="text-sm">{t('tips.purchase')}</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
              <p className="text-sm">
                {t('tips.multiplier', { multiplier: data.pointsMultiplier })}
              </p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
              <p className="text-sm">{t('tips.levelUp')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
