-- Migration: Convert gift thresholds from amount-based to points-based
-- This migration changes the gift threshold system to be based on user points instead of order amount

-- Step 1: Add the new threshold_points column
ALTER TABLE gift_thresholds ADD COLUMN threshold_points INTEGER;

-- Step 2: Copy existing threshold_amount values to threshold_points
-- Assuming 1 CHF = 1 point conversion
UPDATE gift_thresholds SET threshold_points = CAST(threshold_amount AS INTEGER);

-- Step 3: Make threshold_points NOT NULL
ALTER TABLE gift_thresholds ALTER COLUMN threshold_points SET NOT NULL;

-- Step 4: Drop the old threshold_amount column
ALTER TABLE gift_thresholds DROP COLUMN threshold_amount;

-- Step 5: Update any existing data to ensure consistency
-- This ensures all gift thresholds are properly converted
UPDATE gift_thresholds SET updated_at = NOW();
