// VAT configuration types for category-based VAT management

export type ProductCategory = 'coffee' | 'accessories'

export interface CategoryVATRate {
  id: string
  category: ProductCategory
  vat_rate: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface VATCalculationResult {
  inclusive: number
  exclusive: number
  vatAmount: number
  vatRate: number
  category: ProductCategory
}

export interface CategoryVATConfig {
  [key: string]: number // category -> vat_rate mapping
}

export interface VATBreakdown {
  subtotal: number
  vatByCategory: {
    [category: string]: {
      exclusive: number
      vatAmount: number
      vatRate: number
    }
  }
  totalVAT: number
  total: number
}

export interface ProductWithVAT {
  id: string
  category: ProductCategory
  price: number
  discount_price?: number | null
  vatRate: number
  priceExclusive: number
  vatAmount: number
}

// Swiss VAT rates (as of 2024)
export const SWISS_VAT_RATES = {
  STANDARD: 0.077,    // 7.7% - Standard rate
  REDUCED: 0.025,     // 2.5% - Reduced rate (food, books, etc.)
  SPECIAL: 0.037,     // 3.7% - Special rate (accommodation)
} as const

// Default VAT configuration for PrimeCaffe
export const DEFAULT_CATEGORY_VAT_RATES: CategoryVATConfig = {
  coffee: SWISS_VAT_RATES.STANDARD,      // 7.7% for coffee products
  accessories: SWISS_VAT_RATES.STANDARD, // 7.7% for accessories
} as const
