'use client'

import { useTranslations } from 'next-intl'
import { AlertCircle, RefreshCw } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

import { useCartQuery } from '../queries'

interface CartErrorV2Props {
  error: unknown
  compact?: boolean
}

export function CartErrorV2({ error, compact = false }: CartErrorV2Props) {
  const t = useTranslations('cart')
  const { refetch, isRefetching } = useCartQuery()

  const errorMessage = error instanceof Error 
    ? error.message 
    : typeof error === 'string' 
    ? error 
    : t('genericError')

  const handleRetry = () => {
    refetch()
  }

  return (
    <div className={`flex flex-col items-center justify-center space-y-4 ${
      compact ? 'py-8' : 'py-12'
    }`}>
      <Card className="w-full">
        <CardContent className={compact ? 'p-4' : 'p-6'}>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className={compact ? 'text-sm' : ''}>
              {t('errorLoading')}: {errorMessage}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <Button
        variant="outline"
        onClick={handleRetry}
        disabled={isRefetching}
        size={compact ? 'sm' : 'default'}
        className="w-full max-w-xs"
      >
        {isRefetching ? (
          <>
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            {t('retrying')}
          </>
        ) : (
          <>
            <RefreshCw className="mr-2 h-4 w-4" />
            {t('retry')}
          </>
        )}
      </Button>
    </div>
  )
}
