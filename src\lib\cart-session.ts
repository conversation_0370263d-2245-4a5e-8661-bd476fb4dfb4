// Cart V2 Session Management
// Enhanced session management for Cart V2 with secure tokens and lifecycle management

const CART_SESSION_COOKIE = 'cart_session_id'
const CART_SESSION_PREFIX = 'cart_v2_sess'
const MAX_AGE = 60 * 60 * 24 * 30 // 30 days
const SESSION_ID_LENGTH = 32

/**
 * Generate a secure session ID for Cart V2
 */
function generateSecureSessionId(): string {
  const timestamp = Date.now().toString(36)
  const randomPart = Array.from(
    { length: SESSION_ID_LENGTH - timestamp.length - CART_SESSION_PREFIX.length - 1 },
    () => Math.random().toString(36).charAt(2)
  ).join('')
  return `${CART_SESSION_PREFIX}_${timestamp}_${randomPart}`
}

/**
 * Validate session ID format
 */
function isValidSessionId(sessionId: string): boolean {
  if (!sessionId || typeof sessionId !== 'string') return false
  return sessionId.startsWith(CART_SESSION_PREFIX) && sessionId.length >= 20
}

// Server-side functions that use next/headers - only for use in Server Components
export async function getServerCartSessionId(): Promise<string> {
  // Dynamic import to avoid issues when used in client components
  const { cookies } = await import('next/headers')
  const cookieStore = await cookies()
  let session = cookieStore.get(CART_SESSION_COOKIE)?.value

  if (!session || !isValidSessionId(session)) {
    session = generateSecureSessionId()
    cookieStore.set(CART_SESSION_COOKIE, session, {
      path: '/',
      maxAge: MAX_AGE,
      httpOnly: false, // Allow client-side access for cart operations
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    })
  }
  return session
}

export async function clearServerCartSession(): Promise<void> {
  const { cookies } = await import('next/headers')
  const cookieStore = await cookies()
  cookieStore.set(CART_SESSION_COOKIE, '', {
    path: '/',
    maxAge: 0,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  })
}

/**
 * Refresh server session with new expiry
 */
export async function refreshServerCartSession(): Promise<string> {
  const { cookies } = await import('next/headers')
  const cookieStore = await cookies()
  const currentSession = cookieStore.get(CART_SESSION_COOKIE)?.value

  if (currentSession && isValidSessionId(currentSession)) {
    // Refresh existing session
    cookieStore.set(CART_SESSION_COOKIE, currentSession, {
      path: '/',
      maxAge: MAX_AGE,
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    })
    return currentSession
  }

  // Create new session if invalid
  return await getServerCartSessionId()
}

// Client-side functions for browser environments
export function getClientCartSessionId(): string {
  if (typeof document === 'undefined') return 'server-session'

  const match = document.cookie.match(new RegExp(`(?:^|; )${CART_SESSION_COOKIE}=([^;]*)`))
  const existingSession = match?.[1]

  if (existingSession && isValidSessionId(existingSession)) {
    return existingSession
  }

  // Generate new session if none exists or invalid
  const session = generateSecureSessionId()
  const cookieOptions = [
    `${CART_SESSION_COOKIE}=${session}`,
    'path=/',
    `max-age=${MAX_AGE}`,
    process.env.NODE_ENV === 'production' ? 'secure' : '',
    'samesite=lax'
  ].filter(Boolean).join('; ')

  document.cookie = cookieOptions
  return session
}

export function clearClientCartSession(): void {
  if (typeof document === 'undefined') return

  const cookieOptions = [
    `${CART_SESSION_COOKIE}=`,
    'path=/',
    'max-age=0',
    process.env.NODE_ENV === 'production' ? 'secure' : '',
    'samesite=lax'
  ].filter(Boolean).join('; ')

  document.cookie = cookieOptions
}

/**
 * Refresh client session with new expiry
 */
export function refreshClientCartSession(): string {
  const currentSession = getClientCartSessionId()

  if (currentSession && currentSession !== 'server-session' && isValidSessionId(currentSession)) {
    // Refresh existing session
    const cookieOptions = [
      `${CART_SESSION_COOKIE}=${currentSession}`,
      'path=/',
      `max-age=${MAX_AGE}`,
      process.env.NODE_ENV === 'production' ? 'secure' : '',
      'samesite=lax'
    ].filter(Boolean).join('; ')

    document.cookie = cookieOptions
    return currentSession
  }

  // Create new session if invalid
  return getClientCartSessionId()
}

/**
 * Get session info for debugging
 */
export function getSessionInfo(): {
  sessionId: string | null
  isValid: boolean
  isClient: boolean
} {
  const isClient = typeof document !== 'undefined'
  let sessionId: string | null = null

  if (isClient) {
    const match = document.cookie.match(new RegExp(`(?:^|; )${CART_SESSION_COOKIE}=([^;]*)`))
    sessionId = match?.[1] || null
  }

  return {
    sessionId,
    isValid: sessionId ? isValidSessionId(sessionId) : false,
    isClient
  }
}
