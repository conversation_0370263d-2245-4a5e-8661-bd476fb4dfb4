'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { formatCurrency } from '@/lib/utils'
import { Search, Eye, Package, Truck, CheckCircle, XCircle, Plus } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { AdminBackButton } from '@/components/admin/admin-back-button'

export default function AdminOrdersPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [orders, setOrders] = useState<Array<{
    id: string;
    order_number?: string;
    total_amount: number;
    status: string;
    created_at: string;
    shipping_address: Record<string, unknown>;
    tracking_number?: string;
    email: string;
    user_id?: string;
    users: { first_name: string; last_name: string; email: string } | null;
  }>>([])

  const loadOrdersData = useCallback(async () => {
    try {
      console.log('Loading orders data via API...')

      const response = await fetch('/api/admin/orders')

      if (!response.ok) {
        console.error('Failed to fetch orders:', response.status, response.statusText)
        setLoading(false)
        return
      }

      const data = await response.json()

      if (!data.success) {
        console.error('API returned error:', data.error)
        setLoading(false)
        return
      }

      console.log('Orders data received from API:', data.orders)
      console.log('Orders count:', data.orders?.length)

      if (data.orders && data.orders.length > 0) {
        console.log('First order sample:', data.orders[0])
      }

      setOrders(data.orders || [])
      setLoading(false)
      console.log('Orders data loaded successfully, count:', data.orders?.length)
    } catch (error) {
      console.error('Error loading orders data:', error)
      setLoading(false)
    }
  }, [])

  // Filter orders based on search query
  const filteredOrders = useMemo(() => {
    if (!searchQuery.trim()) {
      return orders
    }

    const query = searchQuery.toLowerCase().trim()

    return orders.filter(order => {
      // Search in order number
      if (order.order_number && order.order_number.toLowerCase().includes(query)) {
        return true
      }

      // Search in order ID
      if (order.id.toLowerCase().includes(query)) {
        return true
      }

      // Search in customer email (from order)
      if (order.email && order.email.toLowerCase().includes(query)) {
        return true
      }

      // Search in customer name (from users relation)
      if (order.users) {
        const fullName = `${order.users.first_name} ${order.users.last_name}`.toLowerCase()
        if (fullName.includes(query)) {
          return true
        }

        // Search in customer email (from users relation)
        if (order.users.email && order.users.email.toLowerCase().includes(query)) {
          return true
        }
      }

      // Search in order status
      if (order.status.toLowerCase().includes(query)) {
        return true
      }

      // Search in tracking number
      if (order.tracking_number && order.tracking_number.toLowerCase().includes(query)) {
        return true
      }

      return false
    })
  }, [orders, searchQuery])

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return

        console.log('Checking authentication...')
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.email)

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading orders data')
        setAuthChecked(true)
        console.log('About to call loadOrdersData...')
        await loadOrdersData()
        console.log('loadOrdersData completed')
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, loadOrdersData])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Caricamento ordini...</p>
        </div>
      </div>
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Package className="h-4 w-4" />
      case 'processing':
        return <Package className="h-4 w-4" />
      case 'shipped':
        return <Truck className="h-4 w-4" />
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'processing':
        return 'bg-blue-100 text-blue-800'
      case 'shipped':
        return 'bg-purple-100 text-purple-800'
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('ordersPage.title')}</h1>
          <p className="text-muted-foreground">
            {t('ordersPage.subtitle')}
          </p>
        </div>
        <Button className="bg-green-600 hover:bg-green-700" asChild>
          <Link href={`/${locale}/admin/orders/create`}>
            <Plus className="mr-2 h-4 w-4" />
            {t('ordersPage.createManualOrder')}
          </Link>
        </Button>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('ordersPage.searchPlaceholder')}
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders Statistics */}
      <div className="grid md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('ordersPage.totalOrders')}</p>
                <p className="text-2xl font-bold">{filteredOrders?.length || 0}</p>
              </div>
              <Package className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('ordersPage.pending')}</p>
                <p className="text-2xl font-bold">
                  {filteredOrders?.filter(order => order.status === 'pending').length || 0}
                </p>
              </div>
              <Package className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('ordersPage.shipped')}</p>
                <p className="text-2xl font-bold">
                  {filteredOrders?.filter(order => order.status === 'shipped').length || 0}
                </p>
              </div>
              <Truck className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('ordersPage.delivered')}</p>
                <p className="text-2xl font-bold">
                  {filteredOrders?.filter(order => order.status === 'delivered').length || 0}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('ordersPage.allOrders')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">{t('ordersPage.order')}</th>
                  <th className="text-left py-3 px-4">{t('ordersPage.customer')}</th>
                  <th className="text-left py-3 px-4">{t('ordersPage.date')}</th>
                  <th className="text-left py-3 px-4">{t('ordersPage.amount')}</th>
                  <th className="text-left py-3 px-4">{t('ordersPage.status')}</th>
                  <th className="text-left py-3 px-4">{t('ordersPage.tracking')}</th>
                  <th className="text-left py-3 px-4">{t('ordersPage.actions')}</th>
                </tr>
              </thead>
              <tbody>
                {filteredOrders?.map((order) => {
                  const customer = order.users
                  return (
                    <tr key={order.id} className="border-b hover:bg-muted/50">
                      <td className="py-3 px-4">
                        <div className="font-medium">#{order.order_number || order.id.slice(0, 8)}</div>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium">
                            {customer ? `${customer.first_name} ${customer.last_name}` : 'Guest Order'}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {customer?.email || order.email}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {new Date(order.created_at).toLocaleDateString(locale === 'it' ? 'it-IT' : locale === 'fr' ? 'fr-FR' : 'de-DE')}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-semibold">
                          {formatCurrency(order.total_amount)}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={`${getStatusColor(order.status)} flex items-center gap-1 w-fit`}>
                          {getStatusIcon(order.status)}
                          {order.status}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          {order.tracking_number || '-'}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/${locale}/admin/orders/${order.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
            {(!filteredOrders || filteredOrders.length === 0) && (
              <div className="text-center py-8 text-muted-foreground">
                {searchQuery.trim() ?
                  `No orders found matching "${searchQuery}"` :
                  t('ordersPage.noOrders')
                }
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
