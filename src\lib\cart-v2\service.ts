// Cart V2 Service Layer
// Client-side service layer for cart operations using API routes

import type {
  Cart,
  CartItem,
  CartResponse,
  CartOperationResponse,
  AddToCartParams,
  UpdateCartItemParams,
  RemoveFromCartParams,
} from './types'
import { CartError } from './errors'

export class CartService {
  private baseUrl: string

  constructor() {
    this.baseUrl = '/api/cart-v2'
  }

  /**
   * Get cart for user or session
   */
  async getCart(): Promise<Cart | null> {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for session management
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new CartError(
          errorData.code || 'NETWORK_ERROR',
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        )
      }

      const data: CartResponse = await response.json()
      return data.cart
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Add item to cart using optimized stored procedure
   */
  async addToCart(params: AddToCartParams): Promise<CartItem> {
    const { product_id, quantity } = params

    try {
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          product_id,
          quantity,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new CartError(
          errorData.code || 'NETWORK_ERROR',
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        )
      }

      const data: CartOperationResponse = await response.json()
      if (!data.success || !data.item) {
        throw new CartError('DATABASE_ERROR', 'Failed to add item to cart')
      }

      return data.item
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Update cart item quantity using optimized stored procedure
   */
  async updateCartItem(params: UpdateCartItemParams): Promise<CartItem> {
    const { item_id, quantity } = params

    try {
      const response = await fetch(this.baseUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          item_id,
          quantity,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new CartError(
          errorData.code || 'NETWORK_ERROR',
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        )
      }

      const data: CartOperationResponse = await response.json()
      if (!data.success || !data.item) {
        throw new CartError('DATABASE_ERROR', 'Failed to update cart item')
      }

      return data.item
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Remove item from cart using optimized stored procedure
   */
  async removeFromCart(params: RemoveFromCartParams): Promise<void> {
    const { item_id } = params

    try {
      const response = await fetch(`${this.baseUrl}?item_id=${encodeURIComponent(item_id)}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new CartError(
          errorData.code || 'NETWORK_ERROR',
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        )
      }

      const data = await response.json()
      if (!data.success) {
        throw new CartError('DATABASE_ERROR', 'Failed to remove item from cart')
      }
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Clear entire cart using API route
   */
  async clearCart(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/clear`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new CartError(
          errorData.code || 'NETWORK_ERROR',
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        )
      }

      const data = await response.json()
      if (!data.success) {
        throw new CartError('DATABASE_ERROR', 'Failed to clear cart')
      }
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Sync guest cart to user account after authentication
   */
  async syncGuestCartToUser(guestSessionId: string): Promise<Cart | null> {
    try {
      const response = await fetch(`${this.baseUrl}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          guest_session_id: guestSessionId,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new CartError(
          errorData.code || 'NETWORK_ERROR',
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        )
      }

      const data = await response.json()
      if (!data.success) {
        throw new CartError('DATABASE_ERROR', 'Failed to sync cart')
      }

      return data.cart || null
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

}
