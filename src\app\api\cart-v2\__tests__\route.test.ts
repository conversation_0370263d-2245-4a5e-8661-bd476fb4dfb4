/**
 * @jest-environment node
 */

// Cart V2 API Route Tests
// Tests for Cart V2 API endpoints

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { NextRequest } from 'next/server'
import { CartError } from '../../../../lib/cart-v2/errors'
import type { Cart, CartItem } from '../../../../lib/cart-v2/types'

// Use manual mocks from __mocks__ directory
jest.mock('next/headers')
jest.mock('@supabase/ssr')

// Get access to the mocked Supabase client
const { createServerClient } = require('@supabase/ssr')
const mockSupabaseClient = createServerClient()

// Mock session functions
jest.mock('../../../../lib/cart-session', () => ({
  getServerCartSessionId: jest.fn().mockResolvedValue('test-session-id')
}))

jest.mock('@/lib/cart-session', () => ({
  getServerCartSessionId: jest.fn().mockResolvedValue('test-session-id')
}))

// Mock Supabase server client - both alias and relative paths
jest.mock('../../../../lib/supabase/server', () => ({
  createClient: jest.fn().mockResolvedValue(mockSupabaseClient)
}))

jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn().mockResolvedValue(mockSupabaseClient)
}))

// Set environment variables for Supabase
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'

// Import route handlers AFTER mocks are set up
import { GET, POST, PUT, DELETE } from '../route'

describe('Cart V2 API Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/cart-v2', () => {
    it('should return cart for authenticated user', async () => {
      const mockCart: Cart = {
        id: 'cart-1',
        user_id: 'user-1',
        session_id: null,
        status: 'active',
        total_amount: 25.99,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      const mockSummary = {
        subtotal: 25.99,
        shipping_cost: 0,
        tax_amount: 0,
        discount_amount: 0,
        total: 25.99,
        items_count: 1,
        free_shipping_threshold: 90,
        free_shipping_remaining: 64.01
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      // Mock the Supabase query chain to return the expected cart data
      mockSupabaseClient.maybeSingle.mockResolvedValue({ data: mockCart, error: null })

      const response = await GET()
      const data = await response.json()

      // Test API contract - should return 200 with proper structure
      expect(response.status).toBe(200)
      expect(data).toHaveProperty('cart')
      expect(data).toHaveProperty('summary')
      expect(data.summary).toHaveProperty('subtotal')
      expect(data.summary).toHaveProperty('total')
      expect(data.summary).toHaveProperty('items_count')
      expect(data.summary).toHaveProperty('free_shipping_threshold')
      expect(typeof data.summary.subtotal).toBe('number')
      expect(typeof data.summary.total).toBe('number')
      expect(typeof data.summary.items_count).toBe('number')
    })

    it('should return cart for guest user', async () => {
      const mockCart: Cart = {
        id: 'cart-1',
        user_id: null,
        session_id: 'test-session-id',
        status: 'active',
        total_amount: 15.99,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        items: []
      }

      const mockSummary = {
        subtotal: 15.99,
        shipping_cost: 0,
        tax_amount: 0,
        discount_amount: 0,
        total: 15.99,
        items_count: 1,
        free_shipping_threshold: 90,
        free_shipping_remaining: 74.01
      }

      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      })

      const response = await GET()
      const data = await response.json()

      // Test API contract for guest user
      expect(response.status).toBe(200)
      expect(data).toHaveProperty('cart')
      expect(data).toHaveProperty('summary')
      expect(data.summary).toHaveProperty('subtotal')
      expect(data.summary).toHaveProperty('total')
      expect(data.summary).toHaveProperty('items_count')
      expect(typeof data.summary.subtotal).toBe('number')
      expect(typeof data.summary.total).toBe('number')
    })

    it('should handle cart not found', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      // Mock Supabase to return no cart data
      mockSupabaseClient.maybeSingle.mockResolvedValue({ data: null, error: null })

      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.cart).toBeNull()
      expect(data.summary).toEqual({
        subtotal: 0,
        shipping_cost: 0,
        tax_amount: 0,
        discount_amount: 0,
        total: 0,
        items_count: 0,
        free_shipping_threshold: 90,
        free_shipping_remaining: 90,
      })
    })

    it('should handle database errors gracefully', async () => {
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: { id: 'user-1' } },
        error: null
      })

      const response = await GET()
      const data = await response.json()

      // API should handle errors gracefully and return proper structure
      expect(response.status).toBe(200)
      expect(data).toHaveProperty('cart')
      expect(data).toHaveProperty('summary')
      expect(data.summary).toHaveProperty('subtotal')
      expect(data.summary).toHaveProperty('total')
      expect(typeof data.summary.subtotal).toBe('number')
      expect(typeof data.summary.total).toBe('number')
    })
  })

  describe('POST /api/cart-v2', () => {
    it('should handle product not found error correctly', async () => {
      const requestBody = {
        product_id: 'product-1',
        quantity: 2
      }

      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      // Test API contract - should return proper error structure for non-existent product
      expect(response.status).toBe(404)
      expect(data).toHaveProperty('code')
      expect(data).toHaveProperty('error')
      expect(data.code).toBe('ITEM_NOT_FOUND')
      expect(data.error).toContain('product with id product-1 not found')
    })

    it('should validate request body', async () => {
      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'POST',
        body: JSON.stringify({ invalid: 'data' }),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error')
      expect(data.error).toContain('product_id')
    })

    it('should handle validation errors', async () => {
      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'POST',
        body: JSON.stringify({ product_id: 'product-1', quantity: -1 }),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error')
      expect(data.error).toBe('Invalid quantity. Must be between 1 and 999')
    })
  })

  describe('PUT /api/cart-v2', () => {
    it('should handle update cart item request', async () => {
      const requestBody = {
        item_id: 'item-1',
        quantity: 3
      }

      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'PUT',
        body: JSON.stringify(requestBody),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await PUT(request)
      const data = await response.json()

      // Test API contract - should handle request properly
      expect(response.status).toBeGreaterThanOrEqual(200)
      expect(response.status).toBeLessThan(500)

      // Should have proper error structure for non-existent item
      if (response.status === 404) {
        expect(data).toHaveProperty('code')
        expect(data).toHaveProperty('error')
        expect(data.code).toBe('ITEM_NOT_FOUND')
      }
    })

    it('should handle item not found for update', async () => {
      const request = new NextRequest('http://localhost:3000/api/cart-v2', {
        method: 'PUT',
        body: JSON.stringify({ item_id: 'invalid-item', quantity: 1 }),
        headers: { 'Content-Type': 'application/json' }
      })

      const response = await PUT(request)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data).toHaveProperty('error')
      expect(data).toHaveProperty('code')
      expect(data.code).toBe('ITEM_NOT_FOUND')
    })
  })

  describe('DELETE /api/cart-v2', () => {
    it('should handle remove item request', async () => {
      const request = new NextRequest('http://localhost:3000/api/cart-v2?item_id=item-1')
      const response = await DELETE(request)
      const data = await response.json()

      // Test API contract - should handle request properly
      expect(response.status).toBeGreaterThanOrEqual(200)
      expect(response.status).toBeLessThan(500)

      // Should have proper structure
      if (response.status === 200) {
        expect(data).toHaveProperty('success')
        expect(data.success).toBe(true)
      } else if (response.status === 404) {
        expect(data).toHaveProperty('code')
        expect(data).toHaveProperty('error')
        expect(data.code).toBe('ITEM_NOT_FOUND')
      }
    })

    it('should validate item_id parameter', async () => {
      const request = new NextRequest('http://localhost:3000/api/cart-v2')
      const response = await DELETE(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data).toHaveProperty('error')
      expect(data.error).toContain('item_id')
    })

    it('should handle item not found for delete gracefully', async () => {
      const request = new NextRequest('http://localhost:3000/api/cart-v2?item_id=invalid-item')
      const response = await DELETE(request)
      const data = await response.json()

      // DELETE operation handles non-existent items gracefully
      expect(response.status).toBe(200)
      expect(data).toHaveProperty('success')
      expect(data.success).toBe(true)
    })
  })
})
