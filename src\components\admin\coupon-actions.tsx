'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { MoreHorizontal, Edit, Trash2, ToggleLeft, ToggleRight } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'

interface Coupon {
  id: string
  code: string
  type: 'percentage' | 'fixed_amount'
  value: number
  is_active: boolean
  valid_until: string
}

interface CouponActionsProps {
  coupon: Coupon
}

export function CouponActions({ coupon }: CouponActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const handleToggleActive = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/coupons/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: coupon.id,
          is_active: !coupon.is_active,
        }),
      })

      if (response.ok) {
        toast({
          title: 'Erfolg',
          description: `Gutschein wurde ${!coupon.is_active ? 'aktiviert' : 'deaktiviert'}.`,
        })
        router.refresh()
      } else {
        throw new Error('Fehler beim Aktualisieren')
      }
    } catch {
      toast({
        title: 'Fehler',
        description: 'Gutschein konnte nicht aktualisiert werden.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/coupons/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: coupon.id }),
      })

      if (response.ok) {
        toast({
          title: 'Erfolg',
          description: 'Gutschein wurde gelöscht.',
        })
        router.refresh()
      } else {
        throw new Error('Fehler beim Löschen')
      }
    } catch {
      toast({
        title: 'Fehler',
        description: 'Gutschein konnte nicht gelöscht werden.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
      setShowDeleteDialog(false)
    }
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Menü öffnen</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={() => router.push(`/admin/coupons/edit/${coupon.id}`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Bearbeiten
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleToggleActive} disabled={loading}>
            {coupon.is_active ? (
              <>
                <ToggleLeft className="mr-2 h-4 w-4" />
                Deaktivieren
              </>
            ) : (
              <>
                <ToggleRight className="mr-2 h-4 w-4" />
                Aktivieren
              </>
            )}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setShowDeleteDialog(true)}
            className="text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Löschen
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Gutschein löschen</AlertDialogTitle>
            <AlertDialogDescription>
              Sind Sie sicher, dass Sie den Gutschein &quot;{coupon.code}&quot; löschen möchten?
              Diese Aktion kann nicht rückgängig gemacht werden.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Abbrechen</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={loading}
            >
              {loading ? 'Wird gelöscht...' : 'Löschen'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
