import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import LoginPage from '../app/[locale]/(auth)/login/page'
import RegisterPage from '../app/[locale]/(auth)/register/page'

// Mock the Supabase client
const mockSignInWithPassword = jest.fn()
const mockSignUp = jest.fn()
const mockFrom = jest.fn()
const mockSelect = jest.fn()
const mockEq = jest.fn()
const mockSingle = jest.fn()

jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn(() => ({
    auth: {
      signInWithPassword: mockSignInWithPassword,
      signUp: mockSignUp,
    },
    from: mockFrom.mockReturnValue({
      select: mockSelect.mockReturnValue({
        eq: mockEq.mockReturnValue({
          single: mockSingle.mockResolvedValue({ data: null, error: null })
        })
      }),
      insert: jest.fn().mockResolvedValue({ error: null })
    })
  }))
}))

// Mock Next.js router
const mockPush = jest.fn()
const mockRefresh = jest.fn()

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    refresh: mockRefresh,
  }),
}))

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: (namespace: string) => (key: string) => {
    const translations: Record<string, Record<string, string>> = {
      'auth.login': {
        title: 'Anmelden',
        subtitle: 'Willkommen zurück',
        email: 'E-Mail',
        emailPlaceholder: '<EMAIL>',
        password: 'Passwort',
        submit: 'Anmelden',
        noAccount: 'Noch kein Konto?',
        signUp: 'Registrieren',
        forgotPassword: 'Passwort vergessen?',
        error: 'Anmeldung fehlgeschlagen'
      },
      'auth.register': {
        title: 'Registrieren',
        subtitle: 'Willkommen zurück',
        firstName: 'Vorname',
        lastName: 'Nachname',
        email: 'E-Mail',
        phone: 'Telefon',
        password: 'Passwort',
        confirmPassword: 'Passwort bestätigen',
        submit: 'Anmelden',
        hasAccount: 'Bereits ein Konto?',
        signIn: 'Anmelden'
      }
    }
    return translations[namespace]?.[key] || key
  },
  useLocale: () => 'de'
}))

describe('Authentication Pages', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockFrom.mockReturnValue({
      select: mockSelect.mockReturnValue({
        eq: mockEq.mockReturnValue({
          single: mockSingle.mockResolvedValue({ data: null, error: null })
        })
      }),
      insert: jest.fn().mockResolvedValue({ error: null })
    })
  })

  describe('Login Page', () => {
    it('renders login form correctly', () => {
      render(<LoginPage />)

      expect(screen.getByRole('heading', { name: 'Anmelden' })).toBeInTheDocument()
      expect(screen.getByText('Willkommen zurück')).toBeInTheDocument()
      expect(screen.getByLabelText('E-Mail')).toBeInTheDocument()
      expect(screen.getByLabelText('Passwort')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Anmelden' })).toBeInTheDocument()
    })

    it('shows validation error for empty fields', async () => {
      render(<LoginPage />)

      const submitButton = screen.getByRole('button', { name: 'Anmelden' })
      fireEvent.click(submitButton)

      // HTML5 validation should prevent submission
      const emailInput = screen.getByLabelText('E-Mail')
      expect(emailInput).toBeRequired()
    })

    it('calls signInWithPassword on form submission', async () => {
      mockSignInWithPassword.mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null
      })

      render(<LoginPage />)

      const emailInput = screen.getByLabelText('E-Mail')
      const passwordInput = screen.getByLabelText('Passwort')
      const submitButton = screen.getByRole('button', { name: 'Anmelden' })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSignInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        })
      })
    })

    it('displays error message on login failure', async () => {
      mockSignInWithPassword.mockResolvedValue({
        error: { message: 'Invalid credentials' }
      })

      render(<LoginPage />)

      const emailInput = screen.getByLabelText('E-Mail')
      const passwordInput = screen.getByLabelText('Passwort')
      const submitButton = screen.getByRole('button', { name: 'Anmelden' })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
      })
    })
  })

  describe('Register Page', () => {
    it('renders registration form correctly', () => {
      render(<RegisterPage />)

      expect(screen.getByRole('heading', { name: 'Registrieren' })).toBeInTheDocument()
      expect(screen.getByText('Willkommen zurück')).toBeInTheDocument()
      expect(screen.getByText(/Vorname/)).toBeInTheDocument()
      expect(screen.getByText(/Nachname/)).toBeInTheDocument()
      expect(screen.getByText(/E-Mail/)).toBeInTheDocument()
      expect(screen.getByText('Telefon (optional)')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: 'Anmelden' })).toBeInTheDocument()
    })

    it('has password input fields', () => {
      render(<RegisterPage />)

      const passwordInput = screen.getByPlaceholderText('Mindestens 6 Zeichen')
      const confirmPasswordInput = screen.getByPlaceholderText('Passwort bestätigen')

      expect(passwordInput).toBeInTheDocument()
      expect(confirmPasswordInput).toBeInTheDocument()
      expect(passwordInput).toHaveAttribute('type', 'password')
      expect(confirmPasswordInput).toHaveAttribute('type', 'password')
    })

    it('calls signUp on successful form submission', async () => {
      mockSignUp.mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null
      })

      render(<RegisterPage />)

      // Fill out the form
      fireEvent.change(screen.getByPlaceholderText('Max'), { target: { value: 'Max' } })
      fireEvent.change(screen.getByPlaceholderText('Mustermann'), { target: { value: 'Mustermann' } })
      fireEvent.change(screen.getByPlaceholderText('<EMAIL>'), { target: { value: '<EMAIL>' } })
      fireEvent.change(screen.getByPlaceholderText('Mindestens 6 Zeichen'), { target: { value: 'password123' } })
      fireEvent.change(screen.getByPlaceholderText('Passwort bestätigen'), { target: { value: 'password123' } })

      const submitButton = screen.getByRole('button', { name: 'Anmelden' })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSignUp).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
          options: {
            data: {
              first_name: 'Max',
              last_name: 'Mustermann',
              phone: '',
            }
          }
        })
      })
    })
  })
})
