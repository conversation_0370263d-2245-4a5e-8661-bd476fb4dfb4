'use client'

import { Skeleton } from '@/components/ui/skeleton'
import { Card } from '@/components/ui/card'

interface CartLoadingV2Props {
  itemCount?: number
  compact?: boolean
}

export function CartLoadingV2({ itemCount = 3, compact = false }: CartLoadingV2Props) {
  return (
    <div className="space-y-4">
      {/* Cart Items Skeleton */}
      {Array.from({ length: itemCount }).map((_, index) => (
        <Card key={index} className={`p-4 ${compact ? 'p-3' : ''}`}>
          <div className="flex gap-3">
            {/* Image Skeleton */}
            <Skeleton className={`rounded-md flex-shrink-0 ${
              compact ? 'w-16 h-16' : 'w-20 h-20'
            }`} />
            
            {/* Content Skeleton */}
            <div className="flex-1 space-y-2">
              <div className="flex justify-between items-start">
                <div className="space-y-1 flex-1">
                  <Skeleton className={`h-4 w-3/4 ${compact ? 'h-3' : ''}`} />
                  <Skeleton className={`h-3 w-1/2 ${compact ? 'h-2' : ''}`} />
                </div>
                <Skeleton className={`h-4 w-16 ml-2 ${compact ? 'h-3 w-12' : ''}`} />
              </div>
              
              <div className="flex justify-between items-center">
                <Skeleton className={`h-8 w-24 ${compact ? 'h-6 w-20' : ''}`} />
                <Skeleton className={`h-8 w-8 ${compact ? 'h-6 w-6' : ''}`} />
              </div>
            </div>
          </div>
        </Card>
      ))}

      {/* Summary Skeleton */}
      <Card className={compact ? 'p-3' : ''}>
        <div className={`space-y-3 ${compact ? 'p-3' : 'p-4'}`}>
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="flex justify-between items-center">
              <Skeleton className={`h-4 w-20 ${compact ? 'h-3 w-16' : ''}`} />
              <Skeleton className={`h-4 w-16 ${compact ? 'h-3 w-12' : ''}`} />
            </div>
          ))}
          
          <div className="border-t pt-3">
            <div className="flex justify-between items-center">
              <Skeleton className={`h-5 w-12 ${compact ? 'h-4 w-10' : ''}`} />
              <Skeleton className={`h-5 w-20 ${compact ? 'h-4 w-16' : ''}`} />
            </div>
          </div>
        </div>
      </Card>

      {/* Action Buttons Skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  )
}
