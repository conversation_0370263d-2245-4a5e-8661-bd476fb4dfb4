'use client'

import { useState } from 'react'
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CreditCard, Loader2, Lock } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useToast } from '@/hooks/use-toast'

interface StripePaymentFormProps {
  clientSecret: string
  onSuccess: (paymentIntentId: string) => void
  onError: (error: string) => void
  amount: number
  loading?: boolean
}

const cardElementOptions = {
  style: {
    base: {
      fontSize: '16px',
      color: '#424770',
      '::placeholder': {
        color: '#aab7c4',
      },
      fontFamily: 'system-ui, -apple-system, sans-serif',
    },
    invalid: {
      color: '#9e2146',
    },
  },
  hidePostalCode: true,
}

export function StripePaymentForm({ 
  clientSecret, 
  onSuccess, 
  onError, 
  amount,
  loading = false 
}: StripePaymentFormProps) {
  const stripe = useStripe()
  const elements = useElements()
  const t = useTranslations('checkout')
  const { toast } = useToast()
  const [processing, setProcessing] = useState(false)
  const [cardComplete, setCardComplete] = useState(false)
  const [cardError, setCardError] = useState<string | null>(null)

  const handleCardChange = (event: { complete: boolean; error?: { message: string } }) => {
    setCardComplete(event.complete)
    setCardError(event.error ? event.error.message : null)
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements || processing || loading) {
      return
    }

    const cardElement = elements.getElement(CardElement)
    if (!cardElement) {
      onError('Card element not found')
      return
    }

    setProcessing(true)

    try {
      // Confirm the payment with the card element
      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
        }
      })

      if (error) {
        console.error('Payment confirmation error:', error)
        onError(error.message || 'Payment failed')
        toast({
          title: t('paymentFailed'),
          description: error.message || t('paymentError'),
          variant: 'destructive'
        })
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        onSuccess(paymentIntent.id)
        toast({
          title: t('paymentSuccess'),
          description: t('paymentSuccessDesc'),
          variant: 'default'
        })
      } else if (paymentIntent && paymentIntent.status === 'requires_action') {
        // Handle 3D Secure or other authentication
        toast({
          title: t('authRequired'),
          description: t('authRequiredDesc'),
          variant: 'default'
        })
      }
    } catch (err) {
      console.error('Payment error:', err)
      onError('An unexpected error occurred')
      toast({
        title: t('error'),
        description: t('unexpectedError'),
        variant: 'destructive'
      })
    } finally {
      setProcessing(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('de-CH', {
      style: 'currency',
      currency: 'CHF'
    }).format(amount)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          {t('paymentDetails')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="p-4 border rounded-lg bg-muted/30">
            <CardElement 
              options={cardElementOptions}
              onChange={handleCardChange}
            />
          </div>
          
          {cardError && (
            <div className="text-sm text-red-600 mt-2">
              {cardError}
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Lock className="h-4 w-4" />
              <span>{t('secureSSL')}</span>
            </div>
            <span>{t('total')}: {formatCurrency(amount)}</span>
          </div>

          <Button 
            type="submit"
            className="w-full" 
            size="lg"
            disabled={!stripe || !cardComplete || processing || loading || !!cardError}
          >
            {processing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('processing')}
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                {t('payNow')} - {formatCurrency(amount)}
              </>
            )}
          </Button>

          <div className="text-center text-xs text-muted-foreground">
            <p>{t('dataProtected')}</p>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
