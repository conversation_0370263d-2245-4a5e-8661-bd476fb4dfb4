import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import CouponForm from '@/components/admin/coupon-form'
import { AdminBackButton } from '@/components/admin/admin-back-button'

interface CreateCouponPageProps {
  params: Promise<{
    locale: string
  }>
}

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('admin.couponsPage.create')

  return {
    title: t('title'),
    description: t('subtitle'),
  }
}

export default async function CreateCouponPage({ params }: CreateCouponPageProps) {
  const { locale } = await params
  const supabase = await createClient()

  // Check authentication and admin status
  const { data: { user }, error: authError } = await supabase.auth.getUser()

  if (authError || !user) {
    redirect(`/${locale}/auth/login`)
  }

  // Check if user is admin
  const { data: profile } = await supabase
    .from('profiles')
    .select('is_admin')
    .eq('id', user.id)
    .single()

  if (!profile?.is_admin) {
    redirect(`/${locale}`)
  }

  const t = await getTranslations('admin.couponsPage.create')

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <AdminBackButton />
          <div className="mt-4">
            <h1 className="text-3xl font-bold tracking-tight">{t('title')}</h1>
            <p className="text-muted-foreground mt-2">{t('subtitle')}</p>
          </div>
        </div>

        {/* Form */}
        <CouponForm locale={locale} />
      </div>
    </div>
  )
}
