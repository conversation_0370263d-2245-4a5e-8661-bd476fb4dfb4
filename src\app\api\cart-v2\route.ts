// Cart V2 API Routes
// Main cart operations: GET cart, POST add item, PUT update item, DELETE remove item

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getServerCartSessionId } from '@/lib/cart-session'
import { CartServiceV2 } from '@/lib/cart-v2/server-service'
import { CartError } from '@/lib/cart-v2/errors'
import type {
  AddToCartParams,
  UpdateCartItemParams,
  CartResponse
} from '@/lib/cart-v2/types'

/**
 * GET /api/cart-v2
 * Fetch current cart for authenticated user or session
 */
export async function GET() {
  try {
    const supabase = await createClient()
    const cartService = new CartServiceV2(supabase)
    
    // Get user if authenticated
    const { data: { user } } = await supabase.auth.getUser()
    const userId = user?.id
    
    // Get session ID for guest users
    const sessionId = userId ? null : await getServerCartSessionId()
    
    console.log('🛒 Cart V2 API: GET cart', { userId: userId || 'guest', sessionId })
    
    const cart = await cartService.getCart(userId || undefined, sessionId || undefined)
    const summary = cart ? await cartService.calculateSummary(cart) : null
    
    const response: CartResponse = {
      cart,
      summary: summary || {
        subtotal: 0,
        shipping_cost: 0,
        tax_amount: 0,
        discount_amount: 0,
        total: 0,
        items_count: 0,
        free_shipping_threshold: 90,
        free_shipping_remaining: 90,
      }
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    console.error('🛒 Cart V2 API: GET error:', error)
    
    if (error instanceof CartError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.getHttpStatus() }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/cart-v2
 * Add item to cart
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { product_id, quantity }: AddToCartParams = body
    
    if (!product_id || !quantity) {
      return NextResponse.json(
        { error: 'Missing required fields: product_id, quantity' },
        { status: 400 }
      )
    }
    
    if (quantity <= 0 || quantity > 999) {
      return NextResponse.json(
        { error: 'Invalid quantity. Must be between 1 and 999' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    const cartService = new CartServiceV2(supabase)
    
    // Get user if authenticated
    const { data: { user } } = await supabase.auth.getUser()
    const userId = user?.id
    
    // Get session ID for guest users
    const sessionId = userId ? null : await getServerCartSessionId()
    
    console.log('🛒 Cart V2 API: POST add item', { 
      userId: userId || 'guest', 
      sessionId, 
      product_id, 
      quantity 
    })
    
    const cartItem = await cartService.addToCart({
      product_id,
      quantity,
      user_id: userId || undefined,
      session_id: sessionId || undefined,
    })
    
    return NextResponse.json({
      success: true,
      item: cartItem
    })
    
  } catch (error) {
    console.error('🛒 Cart V2 API: POST error:', error)
    
    if (error instanceof CartError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.getHttpStatus() }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/cart-v2
 * Update cart item quantity
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { item_id, quantity }: UpdateCartItemParams = body
    
    if (!item_id || quantity === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: item_id, quantity' },
        { status: 400 }
      )
    }
    
    if (quantity < 0 || quantity > 999) {
      return NextResponse.json(
        { error: 'Invalid quantity. Must be between 0 and 999' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    const cartService = new CartServiceV2(supabase)
    
    console.log('🛒 Cart V2 API: PUT update item', { item_id, quantity })
    
    if (quantity === 0) {
      // Remove item if quantity is 0
      await cartService.removeFromCart({ item_id })
      return NextResponse.json({ success: true })
    } else {
      // Update quantity
      const cartItem = await cartService.updateCartItem({ item_id, quantity })
      return NextResponse.json({
        success: true,
        item: cartItem
      })
    }
    
  } catch (error) {
    console.error('🛒 Cart V2 API: PUT error:', error)
    
    if (error instanceof CartError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.getHttpStatus() }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/cart-v2
 * Remove item from cart
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const item_id = searchParams.get('item_id')
    
    if (!item_id) {
      return NextResponse.json(
        { error: 'Missing required parameter: item_id' },
        { status: 400 }
      )
    }
    
    const supabase = await createClient()
    const cartService = new CartServiceV2(supabase)
    
    console.log('🛒 Cart V2 API: DELETE remove item', { item_id })
    
    await cartService.removeFromCart({ item_id })
    
    return NextResponse.json({ success: true })
    
  } catch (error) {
    console.error('🛒 Cart V2 API: DELETE error:', error)
    
    if (error instanceof CartError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.getHttpStatus() }
      )
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
