import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export interface LevelManagementConfig {
  id?: string
  total_levels: number
  multiplier_mode: 'uniform' | 'incremental'
  uniform_multiplier: number
  incremental_multiplier_start: number
  incremental_multiplier_step: number
  point_progression_type: 'linear' | 'exponential' | 'custom'
  base_point_increment: number
  exponential_factor: number
  custom_progression_formula?: string
  starting_points: number
  discount_progression_type: 'linear' | 'exponential' | 'custom'
  base_discount_increment: number
  max_discount_percentage: number
  level_naming_pattern: string
  is_active: boolean
}

// GET - Fetch current level management configuration
export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 403 })
    }

    const { data: config, error } = await supabase
      .from('level_management_config')
      .select('*')
      .eq('is_active', true)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching level config:', error)
      return NextResponse.json({ error: 'Fehler beim Laden der Konfiguration' }, { status: 500 })
    }

    return NextResponse.json({ config: config || null })
  } catch (error) {
    console.error('Error in level config GET:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}

// POST - Create or update level management configuration
export async function POST(request: NextRequest) {
  try {
    const data: LevelManagementConfig = await request.json()
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Nicht autorisiert' }, { status: 403 })
    }

    // Validation
    if (data.total_levels < 1 || data.total_levels > 1000) {
      return NextResponse.json({ error: 'Anzahl der Level muss zwischen 1 und 1000 liegen' }, { status: 400 })
    }

    if (data.multiplier_mode === 'uniform' && (data.uniform_multiplier < 0.1 || data.uniform_multiplier > 10)) {
      return NextResponse.json({ error: 'Einheitlicher Multiplikator muss zwischen 0.1 und 10 liegen' }, { status: 400 })
    }

    if (data.base_point_increment < 1) {
      return NextResponse.json({ error: 'Basis-Punkt-Inkrement muss mindestens 1 sein' }, { status: 400 })
    }

    // Deactivate existing configs
    await supabase
      .from('level_management_config')
      .update({ is_active: false })
      .eq('is_active', true)

    // Insert new config
    const { data: config, error } = await supabase
      .from('level_management_config')
      .insert({
        total_levels: data.total_levels,
        multiplier_mode: data.multiplier_mode,
        uniform_multiplier: data.uniform_multiplier,
        incremental_multiplier_start: data.incremental_multiplier_start,
        incremental_multiplier_step: data.incremental_multiplier_step,
        point_progression_type: data.point_progression_type,
        base_point_increment: data.base_point_increment,
        exponential_factor: data.exponential_factor,
        custom_progression_formula: data.custom_progression_formula,
        starting_points: data.starting_points,
        discount_progression_type: data.discount_progression_type,
        base_discount_increment: data.base_discount_increment,
        max_discount_percentage: data.max_discount_percentage,
        level_naming_pattern: data.level_naming_pattern,
        is_active: true,
      })
      .select()
      .single()

    if (error) {
      console.error('Error saving level config:', error)
      return NextResponse.json({ error: 'Fehler beim Speichern der Konfiguration' }, { status: 500 })
    }

    return NextResponse.json({ success: true, config })
  } catch (error) {
    console.error('Error in level config POST:', error)
    return NextResponse.json({ error: 'Interner Serverfehler' }, { status: 500 })
  }
}
