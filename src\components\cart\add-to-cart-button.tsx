'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { ShoppingCart, Check } from 'lucide-react'
import { CartManager } from '@/lib/cart'
import { createClient } from '@/lib/supabase/client'
import { safeGetUser } from '@/lib/supabase/helpers'

interface AddToCartButtonProps {
  productId: string
  quantity?: number
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
  children?: React.ReactNode
}

export function AddToCartButton({
  productId,
  quantity = 1,
  variant = 'default',
  size = 'default',
  className,
  children
}: AddToCartButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isAdded, setIsAdded] = useState(false)
  const t = useTranslations('common')
  const { toast } = useToast()
  const cartManager = new CartManager()
  const supabase = createClient()

  const handleAddToCart = async () => {
    if (isLoading || isAdded) return

    setIsLoading(true)

    try {
      // Get current user
      const user = await safeGetUser(supabase)

      const success = await cartManager.addToCart(productId, quantity, user?.id)

      if (success) {
        setIsAdded(true)
        toast({
          title: t('added'),
          description: t('productAddedToCart'),
        })
        // Reset the "added" state after 2 seconds
        setTimeout(() => setIsAdded(false), 2000)
      } else {
        toast({
          title: t('error'),
          description: t('addToCartError'),
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast({
        title: t('error'),
        description: t('addToCartError'),
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleAddToCart}
      disabled={isLoading}
    >
      {isAdded ? (
        <>
          <Check className="mr-2 h-4 w-4" />
          {t('added')}
        </>
      ) : (
        <>
          {children ? (
            children
          ) : (
            <>
              <ShoppingCart className="mr-2 h-4 w-4" />
              {t('addToCart')}
            </>
          )}
        </>
      )}
    </Button>
  )
}
