"use client";

import { useTranslations } from 'next-intl';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Truck, Clock, MapPin, Package } from 'lucide-react';

export default function ShippingPage() {
  const t = useTranslations('shipping');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-6">
            <Truck className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
          <p className="text-xl text-muted-foreground">
            {t('subtitle')}
          </p>
        </div>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {t('shippingCosts')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3">{t('country')}</th>
                      <th className="text-left p-3">{t('shippingCost')}</th>
                      <th className="text-left p-3">{t('freeShippingFrom')}</th>
                      <th className="text-left p-3">{t('deliveryTime')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b">
                      <td className="p-3">{t('switzerland')}</td>
                      <td className="p-3">CHF 5.90</td>
                      <td className="p-3">CHF 90.00</td>
                      <td className="p-3">1-2 {t('workdays')}</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-3">{t('germany')}</td>
                      <td className="p-3">CHF 9.90</td>
                      <td className="p-3">CHF 100.00</td>
                      <td className="p-3">3-5 {t('workdays')}</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-3">{t('austria')}</td>
                      <td className="p-3">CHF 9.90</td>
                      <td className="p-3">CHF 100.00</td>
                      <td className="p-3">3-5 {t('workdays')}</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-3">{t('france')}</td>
                      <td className="p-3">CHF 12.90</td>
                      <td className="p-3">CHF 150.00</td>
                      <td className="p-3">5-7 {t('workdays')}</td>
                    </tr>
                    <tr className="border-b">
                      <td className="p-3">{t('italy')}</td>
                      <td className="p-3">CHF 12.90</td>
                      <td className="p-3">CHF 150.00</td>
                      <td className="p-3">5-7 {t('workdays')}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {t('processingTime')}
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('processingDescription')}
              </p>
              <p>
                <strong>{t('processingNote')}</strong>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                {t('packaging')}
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('packagingDescription')}
              </p>
              <ul>
                <li>{t('recyclableBoxes')}</li>
                <li>{t('biodegradableFilling')}</li>
                <li>{t('securePadding')}</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('tracking')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('trackingDescription')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('deliveryProblems')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('deliveryProblemsDescription')}
              </p>
              <p>
                <strong>{t('email')}:</strong> <EMAIL><br />
                <strong>{t('phone')}:</strong> +41 44 123 45 67
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
