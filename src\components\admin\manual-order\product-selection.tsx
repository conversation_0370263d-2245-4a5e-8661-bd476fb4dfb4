'use client'

import { useState, useEffect, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Search, Package, Plus, Minus, Trash2 } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import type { Product } from '@/types'

interface OrderItem {
  product: Product
  quantity: number
  unitPrice: number
  totalPrice: number
}

interface ProductSelectionSectionProps {
  orderItems: OrderItem[]
  onItemsChange: (items: OrderItem[]) => void
}

export function ProductSelectionSection({
  orderItems,
  onItemsChange
}: ProductSelectionSectionProps) {
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<Product[]>([])
  const [searching, setSearching] = useState(false)

  // Search for products
  useEffect(() => {
    const searchProducts = async () => {
      if (searchQuery.trim().length < 2) {
        setSearchResults([])
        return
      }

      setSearching(true)
      try {
        const { data, error } = await supabase
          .from('products')
          .select('*')
          .or(`title.ilike.%${searchQuery}%,brand.ilike.%${searchQuery}%,blend.ilike.%${searchQuery}%`)
          .eq('is_available', true)
          .limit(10)

        if (error) {
          console.error('Error searching products:', error)
          setSearchResults([])
        } else {
          setSearchResults(data || [])
        }
      } catch (error) {
        console.error('Error searching products:', error)
        setSearchResults([])
      } finally {
        setSearching(false)
      }
    }

    const debounceTimer = setTimeout(searchProducts, 300)
    return () => clearTimeout(debounceTimer)
  }, [searchQuery, supabase])

  const addProduct = (product: Product) => {
    // Check if product is already in the order
    const existingItemIndex = orderItems.findIndex(item => item.product.id === product.id)
    
    if (existingItemIndex >= 0) {
      // Increase quantity of existing item
      const updatedItems = [...orderItems]
      updatedItems[existingItemIndex].quantity += 1
      updatedItems[existingItemIndex].totalPrice = 
        updatedItems[existingItemIndex].quantity * updatedItems[existingItemIndex].unitPrice
      onItemsChange(updatedItems)
    } else {
      // Add new item
      const unitPrice = product.discount_price || product.price
      const newItem: OrderItem = {
        product,
        quantity: 1,
        unitPrice,
        totalPrice: unitPrice
      }
      onItemsChange([...orderItems, newItem])
    }
    
    setSearchQuery('')
    setSearchResults([])
  }

  const updateQuantity = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(index)
      return
    }

    const updatedItems = [...orderItems]
    updatedItems[index].quantity = newQuantity
    updatedItems[index].totalPrice = newQuantity * updatedItems[index].unitPrice
    onItemsChange(updatedItems)
  }

  const removeItem = (index: number) => {
    const updatedItems = orderItems.filter((_, i) => i !== index)
    onItemsChange(updatedItems)
  }

  const updateUnitPrice = (index: number, newPrice: number) => {
    const updatedItems = [...orderItems]
    updatedItems[index].unitPrice = newPrice
    updatedItems[index].totalPrice = updatedItems[index].quantity * newPrice
    onItemsChange(updatedItems)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          {t('manualOrderPage.productSection.title')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Product Search */}
        <div className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('manualOrderPage.productSection.searchPlaceholder')}
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Search Results */}
          {searching && (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
            </div>
          )}

          {searchResults.length > 0 && (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {searchResults.map((product) => (
                <div
                  key={product.id}
                  className="p-3 border rounded-lg hover:bg-muted transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-medium">{product.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {product.brand && `${product.brand} • `}
                        {product.category}
                        {product.coffee_type && ` • ${product.coffee_type}`}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="font-semibold">
                          {formatCurrency(product.discount_price || product.price)}
                        </span>
                        {product.discount_price && (
                          <span className="text-sm text-muted-foreground line-through">
                            {formatCurrency(product.price)}
                          </span>
                        )}
                        <Badge variant={product.inventory_count > 0 ? 'secondary' : 'destructive'}>
                          {product.inventory_count} in stock
                        </Badge>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => addProduct(product)}
                      disabled={product.inventory_count <= 0}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      {t('manualOrderPage.productSection.addProduct')}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Order Items */}
        <div className="space-y-4">
          <h4 className="font-medium">Order Items</h4>
          
          {orderItems.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {t('manualOrderPage.productSection.noProducts')}
            </div>
          ) : (
            <div className="space-y-3">
              {orderItems.map((item, index) => (
                <div key={`${item.product.id}-${index}`} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="font-medium">{item.product.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {item.product.brand && `${item.product.brand} • `}
                        {item.product.category}
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      {/* Quantity Controls */}
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(index, item.quantity - 1)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) => updateQuantity(index, parseInt(e.target.value) || 1)}
                          className="w-16 text-center"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateQuantity(index, item.quantity + 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>

                      {/* Unit Price */}
                      <div className="text-right">
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={item.unitPrice}
                          onChange={(e) => updateUnitPrice(index, parseFloat(e.target.value) || 0)}
                          className="w-20 text-right"
                        />
                        <div className="text-xs text-muted-foreground">per unit</div>
                      </div>

                      {/* Total Price */}
                      <div className="text-right min-w-[80px]">
                        <div className="font-semibold">
                          {formatCurrency(item.totalPrice)}
                        </div>
                        <div className="text-xs text-muted-foreground">total</div>
                      </div>

                      {/* Remove Button */}
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => removeItem(index)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
