'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Calculator, Coffee, Package } from 'lucide-react'
import { vatService } from '@/lib/services/vat-service'
import type { CategoryVATRate } from '@/lib/types/vat'
import { useToast } from '@/hooks/use-toast'

interface CategoryVATSettingsProps {
  settings: {
    vat_included_in_prices?: boolean
    vat_number?: string | null
  } | null
  saving: boolean
  onSave: (type: string, data: {
    vat_included_in_prices?: boolean
    vat_number?: string | null
  }) => void
  t: (key: string) => string
}

export function CategoryVATSettings({ settings, saving, onSave, t }: CategoryVATSettingsProps) {
  const [categoryRates, setCategoryRates] = useState<CategoryVATRate[]>([])
  const [loading, setLoading] = useState(true)
  const [vatSaving, setVatSaving] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    loadCategoryVATRates()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  const loadCategoryVATRates = async () => {
    try {
      setLoading(true)
      const rates = await vatService.getCategoryVATRates()
      setCategoryRates(rates)
    } catch (error) {
      console.error('Error loading category VAT rates:', error)
      toast({
        title: t('error'),
        description: t('vat.loadError'),
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleVATSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setVatSaving(true)

    try {
      const formData = new FormData(e.currentTarget)
      
      // Update general VAT settings
      const generalVatData = {
        vat_included_in_prices: formData.get('vat-included') === 'on',
        vat_number: formData.get('vat-number') as string || null
      }
      
      // Update category-specific VAT rates
      const categoryUpdates = categoryRates.map(rate => ({
        category: rate.category,
        vat_rate: parseFloat(formData.get(`vat-rate-${rate.category}`) as string) / 100
      }))

      await vatService.updateMultipleCategoryVATRates(categoryUpdates)
      onSave('vat', generalVatData)
      
      // Reload rates to reflect changes
      await loadCategoryVATRates()
      
      toast({
        title: t('success'),
        description: t('vat.updateSuccess')
      })
    } catch (error) {
      console.error('Error updating VAT settings:', error)
      toast({
        title: t('error'),
        description: t('vat.updateError'),
        variant: 'destructive'
      })
    } finally {
      setVatSaving(false)
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'coffee':
        return <Coffee className="h-4 w-4" />
      case 'accessories':
        return <Package className="h-4 w-4" />
      default:
        return null
    }
  }

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'coffee':
        return t('categories.coffee')
      case 'accessories':
        return t('categories.accessories')
      default:
        return category
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            {t('vat.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">{t('loading')}</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          {t('vat.title')}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {t('vat.categoryDescription')}
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleVATSubmit} className="space-y-6">
          {/* Category-specific VAT rates */}
          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">{t('vat.categoryRates')}</Label>
              <p className="text-sm text-muted-foreground">
                {t('vat.categoryRatesDesc')}
              </p>
            </div>
            
            <div className="grid gap-4">
              {categoryRates.map((rate) => (
                <div key={rate.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    {getCategoryIcon(rate.category)}
                    <div>
                      <div className="font-medium">{getCategoryLabel(rate.category)}</div>
                      <Badge variant="outline" className="text-xs">
                        {rate.category}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Label htmlFor={`vat-rate-${rate.category}`} className="text-sm">
                      {t('vat.vatRate')}:
                    </Label>
                    <div className="relative w-24">
                      <Input
                        id={`vat-rate-${rate.category}`}
                        name={`vat-rate-${rate.category}`}
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        defaultValue={(rate.vat_rate * 100).toFixed(2)}
                        className="pr-8 text-right"
                      />
                      <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* General VAT settings */}
          <div className="space-y-4 pt-4 border-t">
            <div>
              <Label className="text-base font-medium">{t('vat.generalSettings')}</Label>
            </div>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="vat-number">{t('vat.vatNumber')}</Label>
                <Input
                  id="vat-number"
                  name="vat-number"
                  defaultValue={settings?.vat_number || ''}
                  placeholder="CHE-123.456.789 MWST"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {t('vat.vatNumberDesc')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="vat-included"
                name="vat-included"
                defaultChecked={settings?.vat_included_in_prices ?? true}
              />
              <div className="grid gap-1.5 leading-none">
                <Label htmlFor="vat-included" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  {t('vat.vatIncluded')}
                </Label>
                <p className="text-xs text-muted-foreground">
                  {t('vat.vatIncludedDesc')}
                </p>
              </div>
            </div>
          </div>

          <Button type="submit" disabled={saving || vatSaving}>
            {(saving || vatSaving) ? t('saving') : t('save')}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
