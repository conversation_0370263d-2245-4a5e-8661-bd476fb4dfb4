#!/usr/bin/env node

/**
 * Fix Cart V2 Linting Issues
 * Automatically fixes common linting issues in the cart v2 files
 */

const fs = require('fs');
const path = require('path');

const cartV2Dir = path.join(process.cwd(), 'src', 'lib', 'cart-v2');

function fixFile(filePath) {
  console.log(`🔧 Fixing ${path.basename(filePath)}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let changes = 0;

  // Fix 'any' types to 'unknown'
  const anyMatches = content.match(/: any(?![a-zA-Z])/g);
  if (anyMatches) {
    content = content.replace(/: any(?![a-zA-Z])/g, ': unknown');
    changes += anyMatches.length;
    console.log(`   • Fixed ${anyMatches.length} 'any' types`);
  }

  // Remove unused parameters from function signatures
  content = content.replace(
    /\(error: unknown, variables[^,)]*(?:, context[^)]*)?/g,
    '(error: unknown'
  );

  // Fix error object access patterns
  content = content.replace(
    /error\.([a-zA-Z_][a-zA-Z0-9_]*)/g,
    '(error as { $1?: unknown }).$1'
  );

  // Simplify error object patterns that were over-complicated
  content = content.replace(
    /\(error as \{ ([a-zA-Z_][a-zA-Z0-9_]*)\?: unknown \}\)\.([a-zA-Z_][a-zA-Z0-9_]*)/g,
    '(error as { $1?: string }).$2'
  );

  if (changes > 0) {
    fs.writeFileSync(filePath, content);
    console.log(`   ✅ Applied ${changes} fixes`);
  } else {
    console.log(`   ✅ No fixes needed`);
  }
}

function removeUnusedImports(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Remove unused imports from types.ts
  if (filePath.includes('queries.ts')) {
    // Remove unused imports
    content = content.replace(/,\s*CartItem\s*,/, ',');
    content = content.replace(/,\s*CartSummary\s*,/, ',');
    content = content.replace(/,\s*CART_QUERY_KEYS\s*,/, ',');
    content = content.replace(/CartItem,\s*/, '');
    content = content.replace(/CartSummary,\s*/, '');
    content = content.replace(/CART_QUERY_KEYS,\s*/, '');
  }

  if (filePath.includes('service.ts')) {
    // Remove unused imports
    content = content.replace(/,\s*CART_QUERY_SELECT\s*,/, ',');
    content = content.replace(/CART_QUERY_SELECT,\s*/, '');
  }

  fs.writeFileSync(filePath, content);
}

function main() {
  console.log('🚀 Fixing Cart V2 Linting Issues\n');

  const files = [
    'errors.ts',
    'queries.ts', 
    'query-client.ts',
    'service.ts',
    'utils.ts'
  ];

  files.forEach(file => {
    const filePath = path.join(cartV2Dir, file);
    if (fs.existsSync(filePath)) {
      fixFile(filePath);
      removeUnusedImports(filePath);
    } else {
      console.log(`⚠️  File not found: ${file}`);
    }
  });

  console.log('\n✅ Cart V2 linting fixes completed!');
  console.log('Run "npm run lint" to verify all issues are resolved.');
}

main();
