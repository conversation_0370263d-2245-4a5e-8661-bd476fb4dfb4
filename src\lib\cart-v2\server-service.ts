// Cart V2 Server-Side Service Layer
// Server-side cart operations with database integration and session management

import type { SupabaseClient } from '@supabase/supabase-js'
import type {
  Cart,
  CartItem,
  CartSummary,
  AddToCartParams,
  UpdateCartItemParams,
  RemoveFromCartParams,
  CartProduct,
} from './types'
import { CartError } from './errors'
import {
  transformCartData,
  transformCartItemData,
  calculateCartSummary,
  validateQuantity,
  sanitizeQuantity,
} from './utils'
import { CART_QUERY_SELECT } from './types'

export class CartServiceV2 {
  private supabase: SupabaseClient

  constructor(supabaseClient: SupabaseClient) {
    this.supabase = supabaseClient
  }

  /**
   * Get cart for user or session with optimized query
   */
  async getCart(userId?: string, sessionId?: string): Promise<Cart | null> {
    try {
      let query = this.supabase
        .from('carts')
        .select(CART_QUERY_SELECT)
        .eq('status', 'active')
        .order('updated_at', { ascending: false })
        .limit(1)

      if (userId) {
        query = query.eq('user_id', userId)
      } else if (sessionId) {
        query = query.eq('session_id', sessionId).is('user_id', null)
      } else {
        throw new CartError('VALIDATION_ERROR', 'Either userId or sessionId must be provided')
      }

      const { data, error } = await query.maybeSingle()

      if (error && (error as { code?: string }).code !== 'PGRST116') {
        throw CartError.fromSupabaseError(error)
      }

      if (!data) return null

      return transformCartData(data)
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Add item to cart using optimized stored procedure
   */
  async addToCart(params: AddToCartParams & { session_id?: string }): Promise<CartItem> {
    const { product_id, quantity, user_id, session_id } = params

    // Validate quantity
    if (!validateQuantity(quantity)) {
      throw CartError.invalidQuantity(quantity)
    }

    // Validate product exists and is available
    await this.validateProduct(product_id)

    try {
      // Use stored procedure for optimized add to cart
      const { data, error } = await this.supabase.rpc('add_to_cart_v2', {
        p_product_id: product_id,
        p_quantity: sanitizeQuantity(quantity),
        p_user_id: user_id || null,
        p_session_id: session_id || null,
      })

      if (error) {
        throw CartError.fromSupabaseError(error)
      }

      if (!data || data.length === 0) {
        throw new CartError('DATABASE_ERROR', 'Failed to add item to cart')
      }

      return transformCartItemData(data[0])
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Update cart item quantity using optimized stored procedure
   */
  async updateCartItem(params: UpdateCartItemParams): Promise<CartItem> {
    const { item_id, quantity } = params

    // Validate quantity
    if (!validateQuantity(quantity)) {
      throw CartError.invalidQuantity(quantity)
    }

    try {
      // Use stored procedure for optimized update
      const { data, error } = await this.supabase.rpc('update_cart_item_quantity_v2', {
        p_cart_item_id: item_id,
        p_quantity: sanitizeQuantity(quantity),
      })

      if (error) {
        throw CartError.fromSupabaseError(error)
      }

      if (!data || data.length === 0) {
        throw CartError.notFound('cart item', item_id)
      }

      return transformCartItemData(data[0])
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Remove item from cart using optimized stored procedure
   */
  async removeFromCart(params: RemoveFromCartParams): Promise<void> {
    const { item_id } = params

    try {
      // Use stored procedure for optimized removal
      const { error } = await this.supabase.rpc('remove_from_cart_v2', {
        p_cart_item_id: item_id,
      })

      if (error) {
        throw CartError.fromSupabaseError(error)
      }
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Clear entire cart using optimized stored procedure
   */
  async clearCart(userId?: string, sessionId?: string): Promise<void> {
    try {
      // Use stored procedure for optimized cart clearing
      const { error } = await this.supabase.rpc('clear_cart_v2', {
        p_user_id: userId || null,
        p_session_id: sessionId || null,
      })

      if (error) {
        throw CartError.fromSupabaseError(error)
      }
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Calculate cart summary with shipping, tax, and discounts
   */
  async calculateSummary(cart: Cart): Promise<CartSummary> {
    return calculateCartSummary(cart)
  }

  /**
   * Validate product exists and is available
   */
  private async validateProduct(productId: string): Promise<CartProduct> {
    try {
      const { data: product, error } = await this.supabase
        .from('products')
        .select('id, title, price, discount_price, images, category, coffee_type, brand, slug, is_available')
        .eq('id', productId)
        .eq('is_available', true)
        .single()

      if (error || !product) {
        throw CartError.notFound('product', productId)
      }

      if (!product.is_available) {
        throw new CartError('PRODUCT_NOT_AVAILABLE', `Product ${product.title} is not available`)
      }

      return product as CartProduct
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromSupabaseError(error)
    }
  }

  /**
   * Sync guest cart to user cart after authentication
   */
  async syncGuestCartToUser(userId: string, guestSessionId: string): Promise<Cart | null> {
    try {
      // Get guest cart
      const guestCart = await this.getCart(undefined, guestSessionId)
      if (!guestCart || !guestCart.items.length) {
        return await this.getCart(userId)
      }

      // Get user cart
      const userCart = await this.getCart(userId)

      // If user has no cart, convert guest cart to user cart
      if (!userCart) {
        const { error } = await this.supabase
          .from('carts')
          .update({
            user_id: userId,
            session_id: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', guestCart.id)

        if (error) {
          throw CartError.fromSupabaseError(error)
        }

        return await this.getCart(userId)
      }

      // Merge guest cart items into user cart
      for (const guestItem of guestCart.items) {
        if (!guestItem.product) continue

        // Check if user cart already has this product
        const existingItem = userCart.items.find(
          item => item.product_id === guestItem.product_id
        )

        if (existingItem) {
          // Update quantity (add guest quantity to existing)
          const newQuantity = Math.min(
            existingItem.quantity + guestItem.quantity,
            999
          )
          await this.updateCartItem({
            item_id: existingItem.id,
            quantity: newQuantity
          })
        } else {
          // Add new item to user cart
          await this.addToCart({
            product_id: guestItem.product_id,
            quantity: guestItem.quantity,
            user_id: userId
          })
        }
      }

      // Clear guest cart
      await this.clearCart(undefined, guestSessionId)

      // Return updated user cart
      return await this.getCart(userId)
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Get cart performance statistics
   */
  async getPerformanceStats(): Promise<Record<string, string>> {
    try {
      const { data, error } = await this.supabase
        .from('cart_performance_stats')
        .select('metric, value')

      if (error) {
        throw CartError.fromSupabaseError(error)
      }

      const stats: Record<string, string> = {}
      data?.forEach(row => {
        stats[row.metric] = row.value
      })

      return stats
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }

  /**
   * Cleanup abandoned carts (for maintenance)
   */
  async cleanupAbandonedCarts(): Promise<number> {
    try {
      const { data, error } = await this.supabase.rpc('cleanup_abandoned_carts')

      if (error) {
        throw CartError.fromSupabaseError(error)
      }

      return data || 0
    } catch (error) {
      if (error instanceof CartError) throw error
      throw CartError.fromNetworkError(error)
    }
  }
}
