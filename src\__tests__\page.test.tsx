import { redirect } from 'next/navigation'
import Home from '../app/page'

// Mock the redirect function
jest.mock('next/navigation', () => ({
  redirect: jest.fn(),
}))

const mockRedirect = redirect as jest.MockedFunction<typeof redirect>

describe('Home', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should redirect to default locale', () => {
    Home()
    expect(mockRedirect).toHaveBeenCalledWith('/de')
  })

  it('should be a function', () => {
    expect(typeof Home).toBe('function')
  })

  it('should call redirect when invoked', () => {
    Home()
    expect(mockRedirect).toHaveBeenCalledTimes(1)
  })
})
