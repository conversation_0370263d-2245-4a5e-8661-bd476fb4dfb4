'use client'

import { useState, useMemo } from 'react'
import { useTranslations } from 'next-intl'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatCurrency } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'
import { ShoppingCart, Coffee } from 'lucide-react'
import { AddToCartButton } from '@/components/cart/add-to-cart-button'

interface Product {
  id: string
  title: string
  price: number
  discount_price?: number
  images?: string[]
  category: string
  coffee_type?: string
  brand?: string
  blend?: string
  machine_compatibility?: string[]
  description?: string
  pack_quantity?: number
  cost_per_espresso?: number
}

interface ShopContentProps {
  products: Product[]
  locale: string
}

export function ShopContent({ products, locale }: ShopContentProps) {
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterBrand, setFilterBrand] = useState<string>('all')
  const [filterBlend, setFilterBlend] = useState<string>('all')
  const [filterCompatibility, setFilterCompatibility] = useState<string>('all')

  const t = useTranslations('shop')

  // Filter products
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      if (filterCategory !== 'all' && product.category !== filterCategory) return false
      if (filterType !== 'all' && product.coffee_type !== filterType) return false
      if (filterBrand !== 'all' && product.brand !== filterBrand) return false
      if (filterBlend !== 'all' && product.blend !== filterBlend) return false
      if (filterCompatibility !== 'all' && product.machine_compatibility && 
          !product.machine_compatibility.some(compat => 
            compat.toLowerCase().includes(filterCompatibility.toLowerCase())
          )) return false
      return true
    })
  }, [products, filterCategory, filterType, filterBrand, filterBlend, filterCompatibility])

  // Get unique values for filters
  const coffeeTypes = useMemo(() => {
    const types = [...new Set(products.map(p => p.coffee_type).filter(Boolean))] as string[]
    return types.sort()
  }, [products])

  const brands = useMemo(() => {
    const brandList = [...new Set(products.map(p => p.brand).filter(Boolean))] as string[]
    return brandList.sort()
  }, [products])

  const categories = useMemo(() => {
    const cats = [...new Set(products.map(p => p.category).filter(Boolean))] as string[]
    return cats.sort()
  }, [products])

  const blends = useMemo(() => {
    const blendList = [...new Set(products.map(p => p.blend).filter(Boolean))] as string[]
    return blendList.sort()
  }, [products])

  const compatibilities = useMemo(() => {
    const compatList = [...new Set(
      products
        .filter(p => p.machine_compatibility && p.machine_compatibility.length > 0)
        .flatMap(p => p.machine_compatibility || [])
        .filter(Boolean)
    )] as string[]
    return compatList.sort()
  }, [products])

  const coffeeProducts = filteredProducts.filter(p => p.category === 'coffee')
  const accessoryProducts = filteredProducts.filter(p => p.category === 'accessories')

  return (
    <>
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          {t('subtitle')}
        </p>
      </div>

      {/* Filters */}
      <Card className="mb-8 border-0 bg-gradient-to-r from-white to-gray-50/50 shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Coffee className="h-5 w-5 text-primary" />
            {t('filters.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.category')}</label>
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-primary/50 focus:border-primary transition-colors">
                  <SelectValue placeholder={t('filters.allCategories')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allCategories')}</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === 'coffee' ? t('coffee') : t('accessories')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.coffeeType')}</label>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-primary/50 focus:border-primary transition-colors">
                  <SelectValue placeholder={t('filters.allTypes')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allTypes')}</SelectItem>
                  {coffeeTypes.map(type => {
                    const translatedType = t(`coffeeTypes.${type}`, { defaultValue: type })
                    return (
                      <SelectItem key={type} value={type}>
                        {translatedType}
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.brand')}</label>
              <Select value={filterBrand} onValueChange={setFilterBrand}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-amber-300 focus:border-amber-500 transition-colors">
                  <SelectValue placeholder={t('filters.allBrands')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allBrands')}</SelectItem>
                  {brands.map(brand => (
                    <SelectItem key={brand} value={brand}>
                      {brand}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.blend')}</label>
              <Select value={filterBlend} onValueChange={setFilterBlend}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-primary/50 focus:border-primary transition-colors">
                  <SelectValue placeholder={t('filters.allBlends')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allBlends')}</SelectItem>
                  {blends.map(blend => (
                    <SelectItem key={blend} value={blend}>
                      {blend}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 block">{t('filters.compatibility')}</label>
              <Select value={filterCompatibility} onValueChange={setFilterCompatibility}>
                <SelectTrigger className="bg-white shadow-sm border-gray-300 hover:border-primary/50 focus:border-primary transition-colors">
                  <SelectValue placeholder={t('filters.allCompatibilities')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('filters.allCompatibilities')}</SelectItem>
                  {compatibilities.map(compatibility => (
                    <SelectItem key={compatibility} value={compatibility}>
                      {compatibility}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Coffee Products */}
      {coffeeProducts.length > 0 && (
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-8">
            <Coffee className="h-8 w-8 text-primary" />
            <h2 className="text-3xl font-bold">{t('coffee')}</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {coffeeProducts.map((product: Product) => (
              <ProductCard key={product.id} product={product} locale={locale} />
            ))}
          </div>
        </section>
      )}

      {/* Accessories */}
      {accessoryProducts.length > 0 && (
        <section className="mb-16">
          <div className="flex items-center gap-3 mb-8">
            <ShoppingCart className="h-8 w-8 text-primary" />
            <h2 className="text-3xl font-bold">{t('accessories')}</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {accessoryProducts.map((product: Product) => (
              <ProductCard key={product.id} product={product} locale={locale} />
            ))}
          </div>
        </section>
      )}

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <div className="text-center py-16">
          <Coffee className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-2xl font-semibold mb-2">{t('noProducts')}</h3>
          <p className="text-muted-foreground">
            {t('noProductsDescription')}
          </p>
        </div>
      )}
    </>
  )
}

function ProductCard({ product, locale }: { product: Product; locale: string }) {
  const t = useTranslations('shop.product')
  const hasDiscount = product.discount_price && product.discount_price < product.price
  const displayPrice = hasDiscount ? (product.discount_price || product.price) : product.price
  const costPerEspresso = product.cost_per_espresso

  return (
    <Card className="group hover-lift hover-glow transition-all-smooth">
      <div className="aspect-square relative overflow-hidden rounded-t-lg">
        {product.images && product.images.length > 0 ? (
          <Image
            src={product.images[0]}
            alt={product.title}
            fill
            className="object-cover group-hover:scale-110 transition-transform-smooth"
          />
        ) : (
          <div className="w-full h-full bg-gradient-card flex items-center justify-center">
            <Coffee className="h-16 w-16 text-muted-foreground group-hover:text-primary transition-colors" />
          </div>
        )}
        {hasDiscount && (
          <Badge className="absolute top-2 right-2 bg-gradient-to-r from-red-500 to-red-600 text-white shadow-medium">
            -{Math.round(((product.price - (product.discount_price || 0)) / product.price) * 100)}%
          </Badge>
        )}
      </div>
      
      <CardContent className="p-4">
        <CardHeader className="p-0 mb-3">
          <CardTitle className="text-lg font-semibold line-clamp-2 group-hover:text-primary transition-colors">
            {product.title}
          </CardTitle>
          {product.description && (
            <CardDescription className="line-clamp-2">
              {product.description}
            </CardDescription>
          )}
        </CardHeader>
        
        {/* Product Details */}
        <div className="space-y-2 mb-4 text-sm text-muted-foreground">
          {product.brand && (
            <div className="flex items-center gap-2">
              <span className="font-medium">Marca:</span>
              <span>{product.brand}</span>
            </div>
          )}
          {product.pack_quantity && (
            <div className="flex items-center gap-2">
              <span className="font-medium">Quantità:</span>
              <span>{product.pack_quantity} pz</span>
            </div>
          )}
          {costPerEspresso && (
            <div className="flex items-center gap-2">
              <span className="font-medium">Per espresso:</span>
              <span>{formatCurrency(costPerEspresso)}</span>
            </div>
          )}
        </div>
        
        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <span className="text-xl font-bold">{formatCurrency(displayPrice)}</span>
            {hasDiscount && (
              <span className="text-sm text-muted-foreground line-through">
                {formatCurrency(product.price)}
              </span>
            )}
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex gap-2">
          <Button asChild className="flex-1">
            <Link href={`/${locale}/shop/product/${product.id}`}>
              {t('details')}
            </Link>
          </Button>
          <AddToCartButton 
            productId={product.id} 
            variant="outline" 
            size="default"
            className="px-3"
          >
            <ShoppingCart className="h-4 w-4" />
          </AddToCartButton>
        </div>
      </CardContent>
    </Card>
  )
}
