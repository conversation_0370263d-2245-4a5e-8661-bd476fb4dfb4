import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { cartId, itemId, quantity } = await request.json()

    if (!cartId || !itemId || quantity === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    if (quantity === 0) {
      // Remove item from cart
      const { error } = await supabase
        .from('cart_items')
        .delete()
        .eq('id', itemId)
        .eq('cart_id', cartId)

      if (error) {
        console.error('Error removing cart item:', error)
        return NextResponse.json(
          { error: 'Failed to remove item from cart' },
          { status: 500 }
        )
      }
    } else {
      // Update item quantity
      const { error } = await supabase
        .from('cart_items')
        .update({ 
          quantity,
          updated_at: new Date().toISOString()
        })
        .eq('id', itemId)
        .eq('cart_id', cartId)

      if (error) {
        console.error('Error updating cart item:', error)
        return NextResponse.json(
          { error: 'Failed to update cart item' },
          { status: 500 }
        )
      }
    }

    // Update cart total
    const { data: cartItems, error: itemsError } = await supabase
      .from('cart_items')
      .select(`
        quantity,
        products (
          price,
          discount_price
        )
      `)
      .eq('cart_id', cartId)

    if (itemsError) {
      console.error('Error fetching cart items for total calculation:', itemsError)
    } else {
      const totalAmount = cartItems.reduce((sum: number, item: {
        quantity: number;
        products: unknown
      }) => {
        const product = item.products as {
          price?: number
          discount_price?: number
        }
        const price = product?.discount_price || product?.price || 0
        return sum + (price * item.quantity)
      }, 0)

      await supabase
        .from('carts')
        .update({ 
          total_amount: totalAmount,
          updated_at: new Date().toISOString()
        })
        .eq('id', cartId)
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Error in cart update API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
